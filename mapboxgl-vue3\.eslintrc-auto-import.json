{"globals": {"Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "DirectiveBinding": true, "EARTH_RADIUS": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "InjectionKey": true, "MaybeRef": true, "MaybeRefOrGetter": true, "MercatorCoordinate": true, "PropType": true, "Ref": true, "Slot": true, "Slots": true, "TRACK_PROGRESS_FACTOR": true, "TerrainConfig": true, "TerrainInfo": true, "TerrainManager": true, "TerrainVarianceConfig": true, "VNode": true, "WritableComputedRef": true, "acceptHMRUpdate": true, "angleDifference": true, "average": true, "bezier": true, "buildMatrix": true, "calculateBearing": true, "calculateBounds": true, "calculateCenter": true, "calculateCumulativeDistances": true, "calculateDestination": true, "calculateDistance": true, "calculateTrackDistance": true, "clamp": true, "computed": true, "createApp": true, "createPinia": true, "customRef": true, "debugMapVisualizationData": true, "debugResponseData": true, "defineAsyncComponent": true, "defineComponent": true, "defineStore": true, "degToRad": true, "distance2D": true, "distance3D": true, "easing": true, "effectScope": true, "getActivePinia": true, "getCurrentInstance": true, "getCurrentScope": true, "getDistanceAtProgress": true, "getInterpolatedPosition": true, "h": true, "identityMatrix": true, "inject": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "lerp": true, "lerpAngle": true, "mapActions": true, "mapGetters": true, "mapRange": true, "mapState": true, "mapStores": true, "mapWritableState": true, "markRaw": true, "max": true, "min": true, "multiplyMat4Mat4": true, "multiplyMat4Vec4": true, "nextTick": true, "normalizeAngle": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "provide": true, "radToDeg": true, "random": true, "randomInt": true, "reactive": true, "readonly": true, "ref": true, "resolveComponent": true, "scale": true, "setActivePinia": true, "setMapStoreSuffix": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "simplifyTrack": true, "smoothstep": true, "storeToRefs": true, "sum": true, "toRaw": true, "toRef": true, "toRefs": true, "toValue": true, "translationMatrix": true, "triggerRef": true, "unref": true, "useAttrs": true, "useCssModule": true, "useCssVars": true, "useDataLoader": true, "useId": true, "useLink": true, "useModel": true, "useRoute": true, "useRouter": true, "useSlots": true, "useTemplateRef": true, "useTerrain": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true}}