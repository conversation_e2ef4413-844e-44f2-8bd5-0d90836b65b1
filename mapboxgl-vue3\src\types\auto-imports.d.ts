/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EARTH_RADIUS: typeof import('../utils/geo')['EARTH_RADIUS']
  const EffectScope: typeof import('vue')['EffectScope']
  const MercatorCoordinate: typeof import('../utils/matrix')['MercatorCoordinate']
  const TRACK_PROGRESS_FACTOR: typeof import('../utils/interpolation')['TRACK_PROGRESS_FACTOR']
  const TerrainManager: typeof import('../utils/terrain')['TerrainManager']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const angleDifference: typeof import('../utils/math')['angleDifference']
  const average: typeof import('../utils/math')['average']
  const bezier: typeof import('../utils/math')['bezier']
  const buildMatrix: typeof import('../utils/matrix')['buildMatrix']
  const calculateBearing: typeof import('../utils/geo')['calculateBearing']
  const calculateBounds: typeof import('../utils/geo')['calculateBounds']
  const calculateCenter: typeof import('../utils/geo')['calculateCenter']
  const calculateCumulativeDistances: typeof import('../utils/interpolation')['calculateCumulativeDistances']
  const calculateDestination: typeof import('../utils/geo')['calculateDestination']
  const calculateDistance: typeof import('../utils/geo')['calculateDistance']
  const calculateTrackDistance: typeof import('../utils/geo')['calculateTrackDistance']
  const clamp: typeof import('../utils/math')['clamp']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const createPinia: typeof import('pinia')['createPinia']
  const customRef: typeof import('vue')['customRef']
  const debugMapVisualizationData: typeof import('../utils/debugData')['debugMapVisualizationData']
  const debugResponseData: typeof import('../utils/debugData')['debugResponseData']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const degToRad: typeof import('../utils/math')['degToRad']
  const distance2D: typeof import('../utils/math')['distance2D']
  const distance3D: typeof import('../utils/math')['distance3D']
  const easing: typeof import('../utils/math')['easing']
  const effectScope: typeof import('vue')['effectScope']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getDistanceAtProgress: typeof import('../utils/interpolation')['getDistanceAtProgress']
  const getInterpolatedPosition: typeof import('../utils/interpolation')['getInterpolatedPosition']
  const h: typeof import('vue')['h']
  const identityMatrix: typeof import('../utils/matrix')['identityMatrix']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const lerp: typeof import('../utils/math')['lerp']
  const lerpAngle: typeof import('../utils/interpolation')['lerpAngle']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapRange: typeof import('../utils/math')['mapRange']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const max: typeof import('../utils/math')['max']
  const min: typeof import('../utils/math')['min']
  const multiplyMat4Mat4: typeof import('../utils/matrix')['multiplyMat4Mat4']
  const multiplyMat4Vec4: typeof import('../utils/matrix')['multiplyMat4Vec4']
  const nextTick: typeof import('vue')['nextTick']
  const normalizeAngle: typeof import('../utils/math')['normalizeAngle']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const provide: typeof import('vue')['provide']
  const radToDeg: typeof import('../utils/math')['radToDeg']
  const random: typeof import('../utils/math')['random']
  const randomInt: typeof import('../utils/math')['randomInt']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const scale: typeof import('../utils/interpolation')['scale']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const simplifyTrack: typeof import('../utils/geo')['simplifyTrack']
  const smoothstep: typeof import('../utils/math')['smoothstep']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const sum: typeof import('../utils/math')['sum']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const translationMatrix: typeof import('../utils/matrix')['translationMatrix']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useDataLoader: typeof import('../composables/useDataLoader')['useDataLoader']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useModel: typeof import('vue')['useModel']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useTerrain: typeof import('../composables/useTerrain')['useTerrain']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { TerrainInfo } from '../composables/useTerrain'
  import('../composables/useTerrain')
  // @ts-ignore
  export type { MercatorCoordinate } from '../utils/matrix'
  import('../utils/matrix')
  // @ts-ignore
  export type { TerrainManager, TerrainConfig, TerrainVarianceConfig } from '../utils/terrain'
  import('../utils/terrain')
}
