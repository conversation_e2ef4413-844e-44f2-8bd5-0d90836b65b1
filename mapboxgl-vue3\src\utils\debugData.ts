/**
 * 数据调试工具
 * 用于检查 response.json 的实际数据结构
 */

export async function debugResponseData() {
  try {
    const response = await fetch('/mock-data/response.json')
    const data = await response.json()
    
    console.log('=== Response.json 数据结构调试 ===')
    console.log('版本:', data.version)
    console.log('运动数据键:', Object.keys(data.workout || {}))
    console.log('扩展数据数量:', data.extensions?.length || 0)
    
    if (data.extensions) {
      console.log('扩展数据类型:')
      data.extensions.forEach((ext: any, index: number) => {
        console.log(`  ${index}: ${ext.type}`)
        if (ext.type === 'LocationStreamExtension') {
          console.log('    LocationStreamExtension 结构:', Object.keys(ext))
          if (ext.locationPoints) {
            console.log('    locationPoints 数量:', ext.locationPoints.length)
            console.log('    第一个点:', ext.locationPoints[0])
          } else {
            console.log('    没有 locationPoints 属性')
            console.log('    实际属性:', Object.keys(ext))
          }
        }
      })
    }
    
    // 检查 workout 中的位置信息
    if (data.workout) {
      console.log('运动起点:', data.workout.startPosition)
      console.log('运动终点:', data.workout.stopPosition)
      console.log('运动中心:', data.workout.centerPosition)
    }
    
    return data
  } catch (error) {
    console.error('调试数据加载失败:', error)
    return null
  }
}

export function debugMapVisualizationData(mapData: any) {
  console.log('=== 地图可视化数据调试 ===')
  console.log('轨迹点数量:', mapData?.trackPoints?.length || 0)
  console.log('边界:', mapData?.bounds)
  console.log('起点:', mapData?.startPoint)
  console.log('终点:', mapData?.endPoint)
  
  if (mapData?.trackPoints?.length > 0) {
    console.log('前3个轨迹点:')
    mapData.trackPoints.slice(0, 3).forEach((point: any, index: number) => {
      console.log(`  ${index}:`, point)
    })
  }
}
