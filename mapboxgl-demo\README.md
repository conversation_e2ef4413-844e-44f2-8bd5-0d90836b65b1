# MapboxGL Better

> 基于 Vue3 + Vite + TypeScript 的高性能地图可视化框架  
> 复刻并超越 maps.suunto.com 的核心能力

## ✨ 特性

- 🚀 **极速构建**: Vite + ESBuild，启动快 10-20 倍
- 🎯 **Vue3 生态**: 组合式 API + TypeScript + Pinia
- 🗺️ **3D 地图**: Mapbox GL JS + WebGL 高性能渲染
- 📱 **响应式设计**: 支持桌面端和移动端
- 🌍 **国际化**: 中英文双语支持
- 🎨 **主题系统**: 明暗主题 + 自动跟随系统
- 📊 **运动数据**: 支持多种运动类型和数据可视化
- 🎮 **相机控制**: 3D 相机路径 + 平滑动画

## 🏗️ 技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| Vue 3 | ^3.4.0 | 前端框架 |
| Vite | ^5.0.0 | 构建工具 |
| TypeScript | ^5.3.0 | 类型系统 |
| Pinia | ^2.1.7 | 状态管理 |
| Vue Router | ^4.2.5 | 路由管理 |
| Mapbox GL JS | ^3.1.0 | 地图引擎 |
| Naive UI | ^2.38.1 | UI 组件库 |
| Vue I18n | ^9.9.0 | 国际化 |

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-username/mapboxgl-better.git
cd mapboxgl-better
```

### 2. 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 3. 配置环境变量

```bash
cp .env.example .env
```

编辑 `.env` 文件，添加你的 Mapbox 访问令牌：

```env
VITE_MAPBOX_TOKEN=your-mapbox-access-token-here
```

> 💡 在 [Mapbox 官网](https://account.mapbox.com/access-tokens/) 获取免费的访问令牌

### 4. 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:3000 查看效果

## 📁 项目结构

```
mapboxgl-better/
├── src/
│   ├── main.ts                 # 应用入口
│   ├── App.vue                 # 根组件
│   ├── router/                 # 路由配置
│   │   └── index.ts
│   ├── stores/                 # Pinia 状态管理
│   │   ├── position.ts         # 播放状态
│   │   ├── measurement.ts      # 单位系统
│   │   └── theme.ts            # 主题管理
│   ├── composables/            # 组合式函数
│   │   └── useCameraPath.ts    # 3D 相机系统
│   ├── components/             # Vue 组件
│   │   ├── MapCanvas.vue       # 地图容器
│   │   ├── CameraControls.vue  # 相机控制
│   │   └── SportSelector.vue   # 运动选择器
│   ├── utils/                  # 工具函数
│   │   ├── math.ts             # 数学计算
│   │   ├── geo.ts              # 地理计算
│   │   └── demo-data.ts        # 演示数据
│   └── types/                  # TypeScript 类型
│       └── index.ts
├── public/
│   └── locales/                # 国际化文件
│       ├── zh.json
│       └── en.json
├── package.json
├── vite.config.ts
└── README.md
```

## 🎮 使用指南

### 基础用法

```vue
<template>
  <MapCanvas 
    :track="demoTrack" 
    :show-controls="true"
    :auto-play="false"
    style="outdoors"
  />
</template>

<script setup>
import { MapCanvas } from '@/components'
import { generateDemoTrack } from '@/utils/demo-data'

const demoTrack = generateDemoTrack()
</script>
```

### 状态管理

```typescript
import { usePositionStore } from '@/stores/position'

const positionStore = usePositionStore()

// 播放控制
positionStore.play()
positionStore.pause()
positionStore.reset()

// 设置播放位置 (0-1)
positionStore.setPosition(0.5)

// 设置播放速度
positionStore.setSpeed(2) // 2倍速
```

### 3D 相机控制

```typescript
import { useCameraPath } from '@/composables/useCameraPath'

const { getCameraPosition, getBounds } = useCameraPath(track)

// 获取指定进度的相机位置
const cameraPos = getCameraPosition(0.5) // 50% 进度
```

## 🔧 开发命令

```bash
# 开发模式
npm run dev

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 🌍 国际化

项目支持中英文双语，语言文件位于 `public/locales/` 目录：

- `zh.json` - 中文
- `en.json` - 英文

添加新语言：

1. 在 `public/locales/` 添加语言文件
2. 在 `src/main.ts` 中导入并配置
3. 在组件中使用 `$t('key')` 或 `useI18n()`

## 🎨 主题系统

支持三种主题模式：

- `light` - 浅色主题
- `dark` - 深色主题  
- `auto` - 跟随系统

```typescript
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

// 切换主题
themeStore.setMode('dark')
themeStore.toggleMode()
```

## 📊 数据格式

### 轨迹数据结构

```typescript
interface Track {
  id: string
  name: string
  sport: SportType
  points: TrackPoint[]
  startTime: number
  endTime: number
  totalDistance: number
  totalTime: number
  maxSpeed: number
  avgSpeed: number
  elevationGain: number
  elevationLoss: number
  maxElevation: number
  minElevation: number
}

interface TrackPoint {
  lat: number
  lng: number
  elevation: number
  time: number
  speed?: number
  heartRate?: number
  cadence?: number
  power?: number
  temperature?: number
}
```

## 🚀 部署

### Vercel 部署

```bash
npm install -g vercel
vercel --prod
```

### Netlify 部署

```bash
npm run build
# 上传 dist 目录到 Netlify
```

### Docker 部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "preview"]
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Mapbox GL JS](https://docs.mapbox.com/mapbox-gl-js/) - 强大的地图引擎
- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Naive UI](https://www.naiveui.com/) - Vue 3 组件库
- [maps.suunto.com](https://maps.suunto.com/) - 设计灵感来源

---

⭐ 如果这个项目对你有帮助，请给个 Star！
