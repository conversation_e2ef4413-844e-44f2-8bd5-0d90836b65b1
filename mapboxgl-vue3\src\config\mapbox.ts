/**
 * Mapbox配置文件
 * 从环境变量中读取Mapbox相关配置
 */

export interface MapboxConfig {
  accessToken: string
  style: string
  terrainSource: string
  terrainExaggeration: number
  debugCamera: boolean
  debugWebGL: boolean
}

export interface TerrainConfig {
  source: string
  exaggeration: number
  enabled: boolean
}

export interface AppConfig {
  title: string
  version: string
  devMode: boolean
}

/**
 * Mapbox配置
 */
export const mapboxConfig: MapboxConfig = {
  accessToken: import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || '',
  style: import.meta.env.VITE_MAPBOX_STYLE || 'mapbox://styles/mapbox/outdoors-v12',
  terrainSource: import.meta.env.VITE_MAPBOX_TERRAIN_SOURCE || 'mapbox-dem',
  terrainExaggeration: Number(import.meta.env.VITE_TERRAIN_EXAGGERATION) || 1.5,
  debugCamera: import.meta.env.VITE_DEBUG_CAMERA === 'true',
  debugWebGL: import.meta.env.VITE_DEBUG_WEBGL === 'true'
}

/**
 * 地形配置
 */
export const terrainConfig: TerrainConfig = {
  source: mapboxConfig.terrainSource,
  exaggeration: mapboxConfig.terrainExaggeration,
  enabled: true
}

/**
 * 应用配置
 */
export const appConfig: AppConfig = {
  title: import.meta.env.VITE_APP_TITLE || 'MapboxGL Vue3',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  devMode: import.meta.env.VITE_DEV_MODE === 'true'
}

/**
 * 验证配置是否完整
 */
export function validateConfig(): boolean {
  if (!mapboxConfig.accessToken) {
    console.error('Mapbox access token is required. Please set VITE_MAPBOX_ACCESS_TOKEN in .env file.')
    return false
  }

  if (!mapboxConfig.style) {
    console.error('Mapbox style is required. Please set VITE_MAPBOX_STYLE in .env file.')
    return false
  }

  return true
}

/**
 * 获取地形数据源配置
 */
export function getTerrainSourceConfig() {
  return {
    type: 'raster-dem',
    url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
    tileSize: 512,
    maxzoom: 14
  }
}
export function getTerrainLayerConfig() {
  return {
    id: 'mapbox-dem',
    source: 'mapbox-dem',
    'source-layer': 'dem',
    minzoom: 1,
    maxzoom: 22
  }
}
