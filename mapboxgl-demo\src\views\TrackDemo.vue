<template>
  <div class="track-demo">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <n-spin size="large">
        <template #description>正在加载轨迹数据...</template>
      </n-spin>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <n-result status="error" title="数据加载失败" :description="error">
        <template #footer>
          <n-button @click="loadTrackData">重新加载</n-button>
        </template>
      </n-result>
    </div>

    <!-- 地图组件 -->
    <MapCanvas
      v-else-if="trackData"
      :track="trackData"
      :show-controls="true"
      :show-sport-selector="false"
      :auto-play="false"
      :style="'outdoors'"
      @track-loaded="handleTrackLoaded"
      @error="handleMapError"
    />

    <!-- 信息面板 -->
    <div v-if="trackData && showInfo" class="info-panel">
      <n-card size="small" title="轨迹信息">
        <n-descriptions :column="1" size="small">
          <n-descriptions-item label="轨迹名称">
            {{ trackData.name }}
          </n-descriptions-item>
          <n-descriptions-item label="数据点数">
            {{ trackData.points.length }}
          </n-descriptions-item>
          <n-descriptions-item label="总距离">
            {{ formatDistance(trackData.totalDistance) }}
          </n-descriptions-item>
          <n-descriptions-item label="总时间">
            {{ formatDuration(trackData.totalTime) }}
          </n-descriptions-item>
          <n-descriptions-item label="平均速度">
            {{ formatSpeed(trackData.avgSpeed) }}
          </n-descriptions-item>
          <n-descriptions-item label="最大速度">
            {{ formatSpeed(trackData.maxSpeed) }}
          </n-descriptions-item>
          <n-descriptions-item label="海拔范围">
            {{ Math.round(trackData.minElevation) }}m - {{ Math.round(trackData.maxElevation) }}m
          </n-descriptions-item>
          <n-descriptions-item label="爬升/下降">
            +{{ Math.round(trackData.elevationGain) }}m / -{{ Math.round(trackData.elevationLoss) }}m
          </n-descriptions-item>
        </n-descriptions>
        
        <template #action>
          <n-space>
            <n-button @click="showInfo = false" size="small">关闭</n-button>
            <n-button @click="resetPlayback" size="small" type="primary">重置播放</n-button>
          </n-space>
        </template>
      </n-card>
    </div>

    <!-- 浮动按钮 -->
    <div class="floating-buttons">
      <n-button-group vertical>
        <n-button @click="showInfo = !showInfo" circle>
          <template #icon>
            <n-icon><InformationCircle /></n-icon>
          </template>
        </n-button>
        <n-button @click="loadTrackData" circle>
          <template #icon>
            <n-icon><Refresh /></n-icon>
          </template>
        </n-button>
      </n-button-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { 
  NSpin, 
  NResult, 
  NButton, 
  NCard, 
  NDescriptions, 
  NDescriptionsItem,
  NSpace,
  NButtonGroup,
  NIcon
} from 'naive-ui'
import { InformationCircle, Refresh } from '@vicons/ionicons5'

import MapCanvas from '@/components/MapCanvas.vue'
import { loadDefaultTrack } from '@/utils/data-loader'
import { useMeasurementStore } from '@/stores/measurement'
import { usePositionStore } from '@/stores/position'
import type { Track } from '@/types'

// Stores
const measurementStore = useMeasurementStore()
const positionStore = usePositionStore()

// 状态
const loading = ref(true)
const error = ref<string>()
const trackData = ref<Track>()
const showInfo = ref(true)

// 格式化函数
const formatDistance = (meters: number) => measurementStore.formatDistance(meters)
const formatSpeed = (metersPerSecond: number) => measurementStore.formatSpeed(metersPerSecond)

const formatDuration = (milliseconds: number) => {
  const totalSeconds = Math.floor(milliseconds / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }
}

// 加载轨迹数据
const loadTrackData = async () => {
  loading.value = true
  error.value = undefined
  
  try {
    trackData.value = await loadDefaultTrack()
    console.log('轨迹数据加载成功:', trackData.value)
    
    // 设置播放时长
    positionStore.setDuration(trackData.value.totalTime)
  } catch (err) {
    error.value = err instanceof Error ? err.message : '未知错误'
    console.error('轨迹数据加载失败:', err)
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleTrackLoaded = (track: Track) => {
  console.log('地图轨迹加载完成:', track.name)
}

const handleMapError = (errorMsg: string) => {
  error.value = `地图错误: ${errorMsg}`
}

const resetPlayback = () => {
  positionStore.reset()
}

// 初始化
onMounted(() => {
  loadTrackData()
})
</script>

<style scoped>
.track-demo {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.loading-container,
.error-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--n-color);
  z-index: 1000;
}

.info-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  max-width: 320px;
  z-index: 1000;
}

.floating-buttons {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-panel {
    top: 10px;
    left: 10px;
    right: 60px;
    max-width: none;
  }
  
  .floating-buttons {
    top: 10px;
    right: 10px;
  }
}
</style>
