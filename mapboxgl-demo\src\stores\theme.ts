import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export type ThemeMode = 'light' | 'dark' | 'auto'

// 主题管理
export const useThemeStore = defineStore('theme', () => {
  // 状态
  const mode = ref<ThemeMode>('auto')
  const systemPrefersDark = ref(false)

  // 计算属性
  const isDark = computed(() => {
    if (mode.value === 'auto') {
      return systemPrefersDark.value
    }
    return mode.value === 'dark'
  })

  const currentTheme = computed(() => isDark.value ? 'dark' : 'light')

  // 监听系统主题变化
  const updateSystemTheme = () => {
    systemPrefersDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }

  // 应用主题到DOM
  const applyTheme = () => {
    const html = document.documentElement
    if (isDark.value) {
      html.classList.add('dark')
      html.setAttribute('data-theme', 'dark')
    } else {
      html.classList.remove('dark')
      html.setAttribute('data-theme', 'light')
    }
  }

  // 动作
  const setMode = (newMode: ThemeMode) => {
    mode.value = newMode
    localStorage.setItem('theme-mode', newMode)
    applyTheme()
  }

  const toggleMode = () => {
    const modes: ThemeMode[] = ['light', 'dark', 'auto']
    const currentIndex = modes.indexOf(mode.value)
    const nextIndex = (currentIndex + 1) % modes.length
    setMode(modes[nextIndex])
  }

  // 初始化
  const initialize = () => {
    // 从本地存储读取
    const stored = localStorage.getItem('theme-mode') as ThemeMode
    if (stored && ['light', 'dark', 'auto'].includes(stored)) {
      mode.value = stored
    }

    // 监听系统主题变化
    updateSystemTheme()
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', updateSystemTheme)

    // 应用初始主题
    applyTheme()

    return () => {
      mediaQuery.removeEventListener('change', updateSystemTheme)
    }
  }

  return {
    // 状态
    mode,
    systemPrefersDark,
    
    // 计算属性
    isDark,
    currentTheme,
    
    // 动作
    setMode,
    toggleMode,
    initialize,
    applyTheme
  }
})
