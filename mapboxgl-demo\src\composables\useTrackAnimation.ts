import { ref, computed, watch } from 'vue'
import mapboxgl, { type Map as MapboxMap } from 'mapbox-gl'
import { DynamicTrackLayer } from '@/layers/DynamicTrackLayer'
import { getInterpolatedPosition } from '@/utils/interpolation'
import type { Track, TrackPoint } from '@/types'

/**
 * 动态轨迹绘制系统
 * 参照 maps.suunto.com 的 LineLayer 实现渐进式轨迹显示
 */
export const useTrackAnimation = (map: MapboxMap, track: Track) => {
  // 状态
  const isInitialized = ref(false)
  const currentPointIndex = ref(0)
  const visiblePoints = ref<TrackPoint[]>([])
  const progressMarker = ref<mapboxgl.Marker>()
  const dynamicTrackLayer = ref<DynamicTrackLayer>()

  // 计算属性
  const totalPoints = computed(() => track?.points.length || 0)
  const progress = computed(() => {
    if (totalPoints.value === 0) return 0
    return currentPointIndex.value / (totalPoints.value - 1)
  })

  /**
   * 初始化动态轨迹系统
   * 使用 WebGL LineLayer 替代 GeoJSON 图层
   */
  const initialize = () => {
    if (!map || !track || isInitialized.value) {
      console.warn('动态轨迹初始化条件不满足:', { map: !!map, track: !!track, isInitialized: isInitialized.value })
      return
    }

    try {
      // 确保地图已完全加载
      if (!map.isStyleLoaded()) {
        console.log('等待地图样式加载完成...')
        map.on('styledata', initialize)
        return
      }

      // 清理已存在的轨迹
      clearExistingTrack()

      // 创建动态轨迹图层
      dynamicTrackLayer.value = new DynamicTrackLayer(map, 'dynamic-track')
      dynamicTrackLayer.value.setTrackPoints(track.points)

      // 适配地图视图到轨迹范围
      dynamicTrackLayer.value.fitToTrack()

      console.log('动态轨迹图层已创建')

      // WebGL LineLayer 已经包含了线条和点的渲染，无需额外图层

      // 创建进度标记
      createProgressMarker()

      isInitialized.value = true
      console.log('动态轨迹系统初始化完成', {
        trackPoints: track.points.length,
        hasDynamicLayer: !!dynamicTrackLayer.value,
        layerId: 'dynamic-track'
      })
    } catch (error) {
      console.error('动态轨迹系统初始化失败:', error)
      isInitialized.value = false
    }
  }

  /**
   * 清理已存在的轨迹
   */
  const clearExistingTrack = () => {
    // 销毁动态轨迹图层
    if (dynamicTrackLayer.value) {
      dynamicTrackLayer.value.destroy()
      dynamicTrackLayer.value = undefined
    }

    // 移除进度标记
    if (progressMarker.value) {
      progressMarker.value.remove()
      progressMarker.value = undefined
    }
  }

  /**
   * 创建进度标记
   */
  const createProgressMarker = () => {
    if (!track.points.length) return

    // 创建自定义标记元素
    const markerElement = document.createElement('div')
    markerElement.className = 'progress-marker'
    markerElement.innerHTML = `
      <div class="marker-pulse"></div>
      <div class="marker-dot"></div>
    `

    // 添加样式
    const style = document.createElement('style')
    style.textContent = `
      .progress-marker {
        width: 20px;
        height: 20px;
        position: relative;
      }
      .marker-dot {
        width: 12px;
        height: 12px;
        background: #ff6b6b;
        border: 2px solid white;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
      }
      .marker-pulse {
        width: 20px;
        height: 20px;
        background: rgba(255, 107, 107, 0.3);
        border-radius: 50%;
        position: absolute;
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0% { transform: scale(1); opacity: 0.7; }
        50% { transform: scale(1.5); opacity: 0.3; }
        100% { transform: scale(2); opacity: 0; }
      }
    `
    document.head.appendChild(style)

    // 创建标记
    const startPoint = track.points[0]
    progressMarker.value = new mapboxgl.Marker(markerElement)
      .setLngLat([startPoint.lng, startPoint.lat])
      .addTo(map)
  }

  /**
   * 更新轨迹显示进度
   * 参照原项目的 setPosition 方法
   */
  const updateProgress = (position: number) => {
    if (!isInitialized.value || !track || !track.points || !track.points.length) {
      console.warn('轨迹进度更新失败: 系统未初始化或无轨迹数据', {
        isInitialized: isInitialized.value,
        hasTrack: !!track,
        hasPoints: !!(track && track.points),
        pointsLength: track?.points?.length || 0
      })
      return
    }

    try {
      // 使用插值计算平滑位置（参照原项目）
      const clampedPosition = Math.max(0, Math.min(1, position))
      const interpolated = getInterpolatedPosition(track.points, clampedPosition)

      currentPointIndex.value = interpolated.pointIndex

      // 更新动态轨迹图层的进度
      if (dynamicTrackLayer.value) {
        dynamicTrackLayer.value.updateProgress(clampedPosition)
      }

      // 更新进度标记位置（使用插值位置）
      if (progressMarker.value) {
        const pos = interpolated.position
        progressMarker.value.setLngLat([pos.lng, pos.lat])
      }

      // 只在调试模式下输出详细日志
      if (position === 0 || position === 1 || Math.random() < 0.01) {
        console.log(`轨迹进度更新: ${(clampedPosition * 100).toFixed(1)}% (距离: ${interpolated.distance.toFixed(2)}km, 插值: ${interpolated.interpolationFactor.toFixed(2)})`)
      }
    } catch (error) {
      console.error('轨迹进度更新失败:', error)
    }
  }

  /**
   * 重置轨迹显示
   */
  const reset = () => {
    currentPointIndex.value = 0
    visiblePoints.value = []
    updateProgress(0)
  }

  /**
   * 获取当前位置的轨迹点
   */
  const getCurrentPoint = computed(() => {
    if (!track.points.length || currentPointIndex.value < 0) return null
    return track.points[Math.min(currentPointIndex.value, track.points.length - 1)]
  })

  /**
   * 获取当前位置的统计信息
   */
  const getCurrentStats = computed(() => {
    const currentPoint = getCurrentPoint.value
    if (!currentPoint) return null

    return {
      distance: currentPointIndex.value > 0 ? 
        track.points.slice(0, currentPointIndex.value + 1)
          .reduce((total, point, index) => {
            if (index === 0) return 0
            const prev = track.points[index - 1]
            return total + calculateDistance(prev, point)
          }, 0) : 0,
      elevation: currentPoint.elevation || 0,
      speed: currentPoint.speed || 0,
      heartRate: currentPoint.heartRate,
      time: currentPoint.time
    }
  })

  /**
   * 计算两点间距离
   */
  const calculateDistance = (point1: TrackPoint, point2: TrackPoint): number => {
    const R = 6371000 // 地球半径（米）
    const dLat = (point2.lat - point1.lat) * Math.PI / 180
    const dLng = (point2.lng - point1.lng) * Math.PI / 180
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  /**
   * 销毁动态轨迹系统
   */
  const destroy = () => {
    clearExistingTrack()
    isInitialized.value = false
    console.log('动态轨迹系统已销毁')
  }

  return {
    // 状态
    isInitialized,
    currentPointIndex,
    visiblePoints,
    progress,
    totalPoints,
    
    // 计算属性
    getCurrentPoint,
    getCurrentStats,
    
    // 方法
    initialize,
    updateProgress,
    reset,
    destroy
  }
}

// 导出类型
export type TrackAnimationComposable = ReturnType<typeof useTrackAnimation>
