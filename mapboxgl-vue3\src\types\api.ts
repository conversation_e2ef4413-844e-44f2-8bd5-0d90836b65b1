/**
 * API响应类型定义 - 基于 Sports Tracker response.json
 */

// Sports Tracker 完整响应结构
export interface SportsTrackerResponse {
  version: number
  camera: CameraConfig
  workout: WorkoutData
  extensions: ExtensionData[]
  minAltitude: number
  maxAltitude: number
  tss: TrainingStressScore
  tssList: TrainingStressScore[]
  zoneSense: any
  suuntoTags: string[]
  achievements: Achievement[]
}

// 相机配置
export interface CameraConfig {
  center: [number, number]
  zoom: number
  bearing: number
  pitch: number
  duration: number
  essential: boolean
}

// 成就信息
export interface Achievement {
  type: string
  title: string
  description: string
  rank?: number
  total?: number
}

// 训练压力评分
export interface TrainingStressScore {
  type: string
  trainingStressScore: number
  intensityFactor?: number
  normalizedPower?: number
}

// 兼容原有接口
export interface ApiResponse {
  success: boolean
  data: {
    workout: WorkoutResponse
    animation: AnimationResponse
    rendering: RenderingResponse
    terrain: TerrainResponse
    metadata: MetadataResponse
  }
  timestamp: string
  version: string
  workout?: any // 兼容现有数据结构
}

// Sports Tracker 运动数据结构
export interface WorkoutData {
  feedType: string
  username: string
  fullname: string
  activityId: number
  key: string
  workoutKey: string
  startTime: number
  stopTime: number
  totalTime: number
  totalDistance: number
  startPosition: Position
  stopPosition: Position
  centerPosition: Position
  maxSpeed: number
  polyline: string
  avgPace: number
  avgSpeed: number
  hrdata: HeartRateData
  energyConsumption: number
  cadence: CadenceData
  stepCount: number
  totalAscent: number
  totalDescent: number
  recoveryTime: number
  rankings: Ranking[]
  userPhoto: string
  coverPhoto: string
}

// 位置信息
export interface Position {
  x: number // longitude
  y: number // latitude
}

// 心率数据
export interface HeartRateData {
  workoutMaxHR: number
  workoutAvgHR: number
  userMaxHR: number
  hrZones: number[]
}

// 步频数据
export interface CadenceData {
  workoutMaxCadence: number
  workoutAvgCadence: number
}

// 排名信息
export interface Ranking {
  type: string
  rank: number
  total: number
  routeName: string
}

// 扩展数据
export interface ExtensionData {
  type: string
  [key: string]: any
}

// 兼容原有接口
export interface WorkoutResponse {
  id: string
  name: string
  type: string
  date: string
  duration: number
  distance: number
  elevationGain: number
  elevationLoss: number
  maxSpeed: number
  avgSpeed: number
  maxHeartRate: number
  avgHeartRate: number
  calories: number
  startLocation: {
    lat: number
    lng: number
    name: string
  }
  endLocation: {
    lat: number
    lng: number
    name: string
  }
}

export interface AnimationResponse {
  duration: number
  fps: number
  totalFrames: number
  cameraSettings: {
    followTrack: boolean
    smoothing: number
    heightOffset: number
    lookAhead: number
  }
}

export interface RenderingResponse {
  trackColor: {
    start: string
    end: string
    encoding: string
  }
  lineWidth: number
  opacity: number
  shadows: boolean
}

export interface TerrainResponse {
  enabled: boolean
  exaggeration: number
  source: string
  varianceImageUrl: string
}

export interface MetadataResponse {
  device: string
  software: string
  gpsAccuracy: string
  samplingRate: number
  dataQuality: number
}

export interface ApiError {
  code: string
  message: string
  details?: any
}

export interface ApiRequestConfig {
  timeout?: number
  retries?: number
  cache?: boolean
}
