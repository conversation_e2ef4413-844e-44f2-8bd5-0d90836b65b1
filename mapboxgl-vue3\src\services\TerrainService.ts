/**
 * 地形服务 - 基于地形方差图动态调整地形夸张度
 * 参考 WorkoutMap.ts 的地形处理逻辑
 */

import mapboxgl, { MercatorCoordinate } from 'mapbox-gl'

/**
 * 地形数据源配置
 */
export interface TerrainSourceConfig {
  id: string
  type: 'raster-dem'
  url?: string
  tiles?: string[]
  tileSize: number
  maxzoom: number
  encoding?: string
  bounds?: number[][]
}

/**
 * 地形夸张度配置
 */
export interface TerrainExaggerationConfig {
  source: string
  exaggeration: number | mapboxgl.Expression
}

/**
 * 地形服务类
 */
export class TerrainService {
  private map: mapboxgl.Map
  private elevationImage: HTMLImageElement
  private elevationContext: CanvasRenderingContext2D | null = null
  private currentDemSource = ''
  private varianceImageUrl = '/dem_std_zoom_4_512.png'

  /**
   * MML DEM 数据源边界坐标（芬兰地区）
   * 每个数组包含四个值：[西南纬度, 西南经度, 东北纬度, 东北经度]
   */
  private readonly MML_DEM_BOUNDS: number[][] = [
    [61.8237, 24.7277, 64.6913, 29.7954],
    [65.946, 23.9773, 68.5443, 28.3443],
    [64.6598, 25.5232, 66.3274, 29.3682],
    [60.9131, 21.8551, 61.907, 28.1602],
    [61.7958, 21.8698, 63.197, 24.8014],
    [63.1637, 22.7243, 63.6386, 29.9574],
    [67.313, 23.9249, 67.8967, 29.3682],
    [68.5319, 26.1015, 69.6914, 28.3149],
    [63.912, 24.1827, 64.2597, 30.1195],
    [66.2919, 28.2412, 68.0844, 28.8747],
    [63.1371, 23.6376, 64.009, 24.8309],
    [62.2176, 29.6775, 63.3692, 30.4583],
    [61.347, 28.0129, 61.9348, 29.0883],
    [68.2023, 23.2546, 68.6008, 24.6394],
    [69.0378, 28.1197, 69.7259, 28.6721],
    [62.5386, 30.3994, 63.1571, 30.9886],
    [69.6863, 26.5581, 69.8772, 28.1491],
    [68.5336, 25.3465, 68.8122, 26.1567],
    [68.8866, 21.1775, 69.2325, 21.6636],
    [68.7455, 21.5089, 69.0004, 21.9582],
    [68.5147, 22.1203, 68.6545, 22.9526],
    [69.3798, 28.6279, 69.6709, 29.0183],
    [68.4241, 22.7832, 68.6076, 23.262],
    [68.6411, 21.7888, 68.8388, 22.1866]
  ]

  private readonly MML_DEM_URL_TEMPLATE = 'https://tileserver-test.sports-tracker.com/mml-dem/{z}/{x}/{y}.png'
  private readonly MML_DEM_MAX_ZOOM = 14

  constructor(map: mapboxgl.Map, varianceImageUrl?: string) {
    this.map = map
    if (varianceImageUrl) {
      this.varianceImageUrl = varianceImageUrl
    }

    this.elevationImage = new Image()
    this.initializeElevationImage()
  }

  /**
   * 初始化地形方差图像
   */
  private initializeElevationImage(): void {
    this.elevationImage.onload = () => {
      // 创建 Canvas 来处理地形方差图像
      const canvas = document.createElement('canvas')
      canvas.width = this.elevationImage.width
      canvas.height = this.elevationImage.height
      const context = canvas.getContext('2d')

      if (context) {
        context.drawImage(this.elevationImage, 0, 0)
        this.elevationContext = context
        console.log('地形方差图像加载完成:', {
          width: this.elevationImage.width,
          height: this.elevationImage.height
        })
      }
    }

    this.elevationImage.onerror = (error) => {
      console.error('地形方差图像加载失败:', error)
    }

    this.elevationImage.src = this.varianceImageUrl
  }

  /**
   * 初始化地形数据源
   */
  async initializeTerrainSources(): Promise<void> {
    console.log('开始初始化地形数据源...')

    return new Promise((resolve) => {
      const checkStyleLoaded = () => {
        if (this.map.isStyleLoaded()) {
          console.log('地图样式已加载，添加地形数据源')
          this.addTerrainSources()

          // 添加数据源后立即应用地形
          setTimeout(() => {
            console.log('应用初始地形设置')
            this.updateTerrain()
            resolve()
          }, 100) // 给数据源一点时间加载

        } else {
          console.log('等待地图样式加载...')
          this.map.once('style.load', () => {
            console.log('地图样式加载完成，添加地形数据源')
            this.addTerrainSources()

            // 添加数据源后立即应用地形
            setTimeout(() => {
              console.log('应用初始地形设置')
              this.updateTerrain()
              resolve()
            }, 100) // 给数据源一点时间加载
          })
        }
      }
      checkStyleLoaded()
    })
  }

  /**
   * 添加地形数据源
   */
  private addTerrainSources(): void {
    console.log('开始添加地形数据源...')

    // 检查地图实例
    if (!this.map) {
      console.error('地图实例不存在')
      return
    }

    console.log('地图实例存在，样式已加载:', this.map.isStyleLoaded())

    // 添加 MML DEM 数据源（芬兰地区的高精度地形数据）
    if (!this.map.getSource('mml-dem')) {
      console.log('添加 MML DEM 数据源...')
      this.map.addSource('mml-dem', {
        type: 'raster-dem',
        tiles: [this.MML_DEM_URL_TEMPLATE],
        tileSize: 512,
        maxzoom: this.MML_DEM_MAX_ZOOM,
        encoding: 'mapbox'
      })
      console.log('MML DEM 数据源已添加')
    } else {
      console.log('MML DEM 数据源已存在')
    }

    // 添加 Mapbox DEM 数据源（全球地形数据）
    if (!this.map.getSource('mapbox-dem')) {
      console.log('添加 Mapbox DEM 数据源...')
      this.map.addSource('mapbox-dem', {
        type: 'raster-dem',
        url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
        tileSize: 512,
        maxzoom: 14
      })
      console.log('Mapbox DEM 数据源已添加')
    } else {
      console.log('Mapbox DEM 数据源已存在')
    }

    // 验证数据源是否成功添加
    const mmlSource = this.map.getSource('mml-dem')
    const mapboxSource = this.map.getSource('mapbox-dem')

    console.log('地形数据源验证:', {
      mmlDem: !!mmlSource,
      mapboxDem: !!mapboxSource,
      mmlType: mmlSource?.type,
      mapboxType: mapboxSource?.type
    })

    console.log('地形数据源初始化完成')
  }

  /**
   * 检查坐标是否在芬兰境内（MML DEM 覆盖范围）
   */
  private isInFinland(center: mapboxgl.LngLat): boolean {
    for (const box of this.MML_DEM_BOUNDS) {
      if (
        center.lat >= box[0] &&
        center.lat <= box[2] &&
        center.lng >= box[1] &&
        center.lng <= box[3]
      ) {
        return true
      }
    }
    return false
  }

  /**
   * 根据地形方差图计算地形夸张度
   */
  private calculateExaggeration(center: mapboxgl.LngLat): number {
    let exaggeration = 1.35 // 默认地形夸张度

    if (this.elevationContext) {
      // 获取地形方差图像的尺寸
      const size = this.elevationImage.width

      // 将地理坐标转换为墨卡托投影坐标
      const xy = MercatorCoordinate.fromLngLat(center)

      try {
        // 从地形方差图像中获取当前位置的地形变化值
        // 使用蓝色通道值表示地形变化程度
        const imageData = this.elevationContext.getImageData(
          Math.floor(xy.x * size),
          Math.floor(xy.y * size),
          1,
          1
        )
        const variance = imageData.data[2] / 255 // 蓝色通道值，范围 0-1

        // 根据地形变化值计算地形夸张度
        // 地形变化大的地方（山地）需要更大的夸张度
        // 地形变化小的地方（平原）需要较小的夸张度
        exaggeration = 1 + (1 - variance) * 0.7

        console.log('地形夸张度计算:', {
          position: [center.lng, center.lat],
          variance: variance.toFixed(3),
          exaggeration: exaggeration.toFixed(3)
        })
      } catch (error) {
        console.warn('获取地形方差数据失败，使用默认夸张度:', error)
      }
    }

    return exaggeration
  }

  /**
   * 更新地形设置
   * 根据当前地图中心位置和缩放级别智能选择地形数据源和夸张度
   */
  updateTerrain(): void {
    console.log('开始更新地形...')

    if (!this.map) {
      console.error('地图实例不存在，无法更新地形')
      return
    }

    const center = this.map.getCenter()
    const zoom = this.map.getZoom()

    console.log('当前地图状态:', {
      center: [center.lng.toFixed(6), center.lat.toFixed(6)],
      zoom: zoom.toFixed(1),
      styleLoaded: this.map.isStyleLoaded()
    })

    let source = 'mapbox-dem' // 默认使用 Mapbox DEM

    // 在缩放级别 >= 10 时，检查是否在芬兰境内
    if (zoom >= 10 && this.isInFinland(center)) {
      source = 'mml-dem' // 在芬兰境内使用 MML DEM
    }

    console.log('选择的地形数据源:', source)

    // 验证数据源是否存在
    const selectedSource = this.map.getSource(source)
    if (!selectedSource) {
      console.error(`地形数据源 ${source} 不存在`)
      return
    }

    console.log('地形数据源验证通过:', selectedSource.type)

    // 计算地形夸张度 - 暂时使用固定值进行测试
    const exaggeration = 2.0 // this.calculateExaggeration(center)

    console.log('使用的地形夸张度:', exaggeration.toFixed(3))

    // 如果数据源发生变化或者是首次设置，更新地形
    if (source !== this.currentDemSource || this.currentDemSource === '') {
      console.log('应用地形设置...')

      const terrainConfig: TerrainExaggerationConfig = {
        source,
        exaggeration: [
          'interpolate', // 插值函数
          ['linear'], // 线性插值
          ['zoom'], // 基于缩放级别
          8, // 缩放级别 8
          exaggeration, // 对应的夸张度
          12, // 缩放级别 12
          exaggeration // 对应的夸张度
        ]
      }

      console.log('地形配置:', terrainConfig)

      try {
        this.map.setTerrain(terrainConfig)
        this.currentDemSource = source

        console.log('地形更新成功:', {
          source,
          exaggeration: exaggeration.toFixed(3),
          zoom: zoom.toFixed(1),
          center: [center.lng.toFixed(6), center.lat.toFixed(6)]
        })

        // 验证地形是否已设置
        const currentTerrain = this.map.getTerrain()
        console.log('当前地形设置:', currentTerrain)

      } catch (error) {
        console.error('设置地形失败:', error)
      }
    } else {
      console.log('地形数据源未变化，跳过更新')
    }
  }

  /**
   * 启用地形自动更新
   * 监听地图移动和缩放事件，自动更新地形设置
   */
  enableAutoUpdate(): void {
    // 监听地图空闲事件（移动或缩放结束后）
    this.map.on('idle', () => {
      this.updateTerrain()
    })

    // 监听地图移动事件
    this.map.on('moveend', () => {
      this.updateTerrain()
    })

    // 监听缩放事件
    this.map.on('zoomend', () => {
      this.updateTerrain()
    })

    console.log('地形自动更新已启用')
  }

  /**
   * 禁用地形
   */
  disableTerrain(): void {
    this.map.setTerrain(null)
    this.currentDemSource = ''
    console.log('地形已禁用')
  }

  /**
   * 获取当前地形信息
   */
  getCurrentTerrainInfo(): {
    source: string
    exaggeration: number
    center: [number, number]
    zoom: number
  } {
    const center = this.map.getCenter()
    const zoom = this.map.getZoom()
    const exaggeration = this.calculateExaggeration(center)

    return {
      source: this.currentDemSource,
      exaggeration,
      center: [center.lng, center.lat],
      zoom
    }
  }

  /**
   * 销毁服务，清理资源
   */
  destroy(): void {
    // 移除事件监听器
    this.map.off('idle', this.updateTerrain)
    this.map.off('moveend', this.updateTerrain)
    this.map.off('zoomend', this.updateTerrain)

    // 清理图像资源
    this.elevationContext = null

    console.log('地形服务已销毁')
  }
}
