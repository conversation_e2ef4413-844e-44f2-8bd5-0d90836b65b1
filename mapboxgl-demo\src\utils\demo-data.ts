import type { Track, TrackPoint } from '@/types'

// 生成演示轨迹数据
export const generateDemoTrack = (): Track => {
  const startTime = Date.now() - 3600000 // 1小时前开始
  const points: TrackPoint[] = []
  
  // 北京天安门附近的演示路线
  const baseLatLng = { lat: 39.9042, lng: 116.4074 }
  const totalPoints = 100
  const duration = 3600000 // 1小时
  
  for (let i = 0; i < totalPoints; i++) {
    const progress = i / (totalPoints - 1)
    const time = startTime + progress * duration
    
    // 生成螺旋形轨迹
    const angle = progress * Math.PI * 4
    const radius = 0.01 * (1 + progress * 0.5)
    
    const lat = baseLatLng.lat + Math.cos(angle) * radius
    const lng = baseLatLng.lng + Math.sin(angle) * radius
    const elevation = 50 + Math.sin(progress * Math.PI * 2) * 20 // 模拟海拔变化
    
    // 模拟速度变化
    const speed = 3 + Math.sin(progress * Math.PI * 6) * 2 // 1-5 m/s
    
    points.push({
      lat,
      lng,
      elevation,
      time,
      speed,
      heartRate: 120 + Math.sin(progress * Math.PI * 8) * 20, // 100-140 bpm
      cadence: 160 + Math.sin(progress * Math.PI * 10) * 20, // 140-180 spm
      temperature: 20 + Math.sin(progress * Math.PI) * 5 // 15-25°C
    })
  }
  
  // 计算统计数据
  const totalDistance = calculateTotalDistance(points)
  const speeds = points.map(p => p.speed || 0).filter(s => s > 0)
  const elevations = points.map(p => p.elevation || 0)
  
  return {
    id: 'demo-track-1',
    name: '北京天安门广场跑步',
    sport: 'running',
    points,
    startTime,
    endTime: startTime + duration,
    totalDistance,
    totalTime: duration,
    maxSpeed: Math.max(...speeds),
    avgSpeed: speeds.reduce((a, b) => a + b, 0) / speeds.length,
    elevationGain: calculateElevationGain(elevations),
    elevationLoss: calculateElevationLoss(elevations),
    maxElevation: Math.max(...elevations),
    minElevation: Math.min(...elevations)
  }
}

// 计算总距离
const calculateTotalDistance = (points: TrackPoint[]): number => {
  let distance = 0
  for (let i = 1; i < points.length; i++) {
    const prev = points[i - 1]
    const curr = points[i]
    distance += haversineDistance(prev.lat, prev.lng, curr.lat, curr.lng)
  }
  return distance
}

// 计算爬升
const calculateElevationGain = (elevations: number[]): number => {
  let gain = 0
  for (let i = 1; i < elevations.length; i++) {
    const diff = elevations[i] - elevations[i - 1]
    if (diff > 0) gain += diff
  }
  return gain
}

// 计算下降
const calculateElevationLoss = (elevations: number[]): number => {
  let loss = 0
  for (let i = 1; i < elevations.length; i++) {
    const diff = elevations[i] - elevations[i - 1]
    if (diff < 0) loss += Math.abs(diff)
  }
  return loss
}

// Haversine距离计算
const haversineDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
  const R = 6371000 // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

// 生成多个演示轨迹
export const generateMultipleDemoTracks = (): Track[] => {
  return [
    generateDemoTrack(),
    {
      ...generateDemoTrack(),
      id: 'demo-track-2',
      name: '骑行环游故宫',
      sport: 'cycling'
    },
    {
      ...generateDemoTrack(),
      id: 'demo-track-3',
      name: '徒步颐和园',
      sport: 'hiking'
    }
  ]
}
