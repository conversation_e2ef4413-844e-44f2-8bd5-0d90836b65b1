/**
 * 地形方差图像生成器
 * 用于生成测试用的地形变化检测图像
 */
export class TerrainImageGenerator {
  /**
   * 生成简单的地形方差图像
   * 基于经纬度生成模拟的地形变化数据
   */
  static generateTerrainVarianceImage(width: number = 512, height: number = 256): HTMLCanvasElement {
    const canvas = document.createElement('canvas')
    canvas.width = width
    canvas.height = height
    
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('无法创建 Canvas 上下文')
    }

    const imageData = ctx.createImageData(width, height)
    const data = imageData.data

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * 4

        // 将像素坐标转换为地理坐标
        const lng = (x / width) * 360 - 180  // -180 to 180
        const lat = 90 - (y / height) * 180  // 90 to -90

        // 生成基于位置的地形方差值
        const variance = this.calculateMockVariance(lng, lat)
        
        // 将方差值 (0-1) 映射到颜色
        const value = Math.floor(variance * 255)
        
        // RGB 通道设置为相同值，蓝色通道存储方差数据
        data[index] = value     // Red
        data[index + 1] = value // Green  
        data[index + 2] = value // Blue (用于方差检测)
        data[index + 3] = 255   // Alpha
      }
    }

    ctx.putImageData(imageData, 0, 0)
    return canvas
  }

  /**
   * 计算模拟的地形方差
   * 基于地理位置生成合理的地形变化值
   */
  private static calculateMockVariance(lng: number, lat: number): number {
    // 基础噪声函数
    const noise1 = Math.sin(lng * 0.1) * Math.cos(lat * 0.1)
    const noise2 = Math.sin(lng * 0.05) * Math.sin(lat * 0.05) * 0.5
    const noise3 = Math.sin(lng * 0.02) * Math.cos(lat * 0.02) * 0.25
    
    // 组合噪声
    let variance = (noise1 + noise2 + noise3) * 0.5 + 0.5
    
    // 添加一些地理特征
    // 山脉区域 (阿尔卑斯山、喜马拉雅山等)
    if ((lng > 5 && lng < 15 && lat > 45 && lat < 48) || // 阿尔卑斯山
        (lng > 80 && lng < 100 && lat > 25 && lat < 35)) { // 喜马拉雅山
      variance = Math.min(variance + 0.3, 1.0)
    }
    
    // 海洋区域 (较低的地形变化)
    if (Math.abs(lat) < 60) {
      // 模拟海洋区域
      const oceanNoise = Math.sin(lng * 0.01) * Math.cos(lat * 0.01) * 0.1
      if (oceanNoise < -0.05) {
        variance = Math.max(variance - 0.4, 0.1)
      }
    }
    
    // 确保值在 0-1 范围内
    return Math.max(0, Math.min(1, variance))
  }

  /**
   * 将 Canvas 转换为 Blob URL
   */
  static canvasToBlob(canvas: HTMLCanvasElement): Promise<string> {
    return new Promise((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob)
          resolve(url)
        } else {
          reject(new Error('无法创建 Blob'))
        }
      }, 'image/png')
    })
  }

  /**
   * 生成并保存地形方差图像到 public 目录
   */
  static async generateAndSave(): Promise<string> {
    try {
      const canvas = this.generateTerrainVarianceImage()
      const blobUrl = await this.canvasToBlob(canvas)
      
      console.log('地形方差图像生成完成')
      return blobUrl
    } catch (error) {
      console.error('地形方差图像生成失败:', error)
      throw error
    }
  }
}
