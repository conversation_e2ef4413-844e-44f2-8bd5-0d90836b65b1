<template>
  <div class="api-demo">
    <div class="demo-header">
      <h2>Sports Tracker API 演示</h2>
      <p>基于 response.json 数据的模拟 API 调用</p>
    </div>

    <div class="demo-controls">
      <button @click="loadAllData" :disabled="isLoading" class="btn-primary">
        {{ isLoading ? '加载中...' : '加载所有数据' }}
      </button>
      <button @click="loadSpecificData" :disabled="isLoading" class="btn-secondary">
        加载特定数据
      </button>
      <button @click="clearData" class="btn-danger">
        清除数据
      </button>
    </div>

    <div v-if="error" class="error-message">
      错误: {{ error }}
    </div>

    <div class="demo-content">
      <!-- 运动摘要 -->
      <div v-if="workoutSummary" class="data-section">
        <h3>运动摘要</h3>
        <div class="summary-grid">
          <div class="summary-item">
            <label>运动员:</label>
            <span>{{ workoutSummary.athlete }}</span>
          </div>
          <div class="summary-item">
            <label>运动类型:</label>
            <span>{{ workoutSummary.activityType }}</span>
          </div>
          <div class="summary-item">
            <label>开始时间:</label>
            <span>{{ workoutSummary.startTime }}</span>
          </div>
          <div class="summary-item">
            <label>持续时间:</label>
            <span>{{ workoutSummary.duration }}</span>
          </div>
          <div class="summary-item">
            <label>距离:</label>
            <span>{{ workoutSummary.distance }}</span>
          </div>
          <div class="summary-item">
            <label>平均配速:</label>
            <span>{{ workoutSummary.avgPace }}</span>
          </div>
          <div class="summary-item">
            <label>平均心率:</label>
            <span>{{ workoutSummary.avgHeartRate }}</span>
          </div>
          <div class="summary-item">
            <label>消耗卡路里:</label>
            <span>{{ workoutSummary.calories }}</span>
          </div>
        </div>
      </div>

      <!-- 传感器数据统计 -->
      <div v-if="extractedSensorData" class="data-section">
        <h3>传感器数据统计</h3>
        <div class="sensor-stats">
          <div class="stat-card">
            <h4>海拔</h4>
            <p>最小: {{ extractedSensorData.altitude.min.toFixed(1) }}m</p>
            <p>最大: {{ extractedSensorData.altitude.max.toFixed(1) }}m</p>
            <p>平均: {{ extractedSensorData.altitude.avg.toFixed(1) }}m</p>
            <p>数据点: {{ extractedSensorData.altitude.values.length }}</p>
          </div>
          <div class="stat-card">
            <h4>心率</h4>
            <p>最小: {{ extractedSensorData.heartRate.min }}bpm</p>
            <p>最大: {{ extractedSensorData.heartRate.max }}bpm</p>
            <p>平均: {{ extractedSensorData.heartRate.avg.toFixed(1) }}bpm</p>
            <p>数据点: {{ extractedSensorData.heartRate.values.length }}</p>
          </div>
          <div class="stat-card">
            <h4>速度</h4>
            <p>最小: {{ extractedSensorData.speed.min.toFixed(2) }}m/s</p>
            <p>最大: {{ extractedSensorData.speed.max.toFixed(2) }}m/s</p>
            <p>平均: {{ extractedSensorData.speed.avg.toFixed(2) }}m/s</p>
            <p>数据点: {{ extractedSensorData.speed.values.length }}</p>
          </div>
        </div>
      </div>

      <!-- 地图数据 -->
      <div v-if="mapVisualizationData" class="data-section">
        <h3>地图数据</h3>
        <div class="map-stats">
          <p><strong>轨迹点数量:</strong> {{ mapVisualizationData.trackPoints.length }}</p>
          <p><strong>起点:</strong> {{ mapVisualizationData.startPoint.latitude.toFixed(6) }}, {{ mapVisualizationData.startPoint.longitude.toFixed(6) }}</p>
          <p><strong>终点:</strong> {{ mapVisualizationData.endPoint.latitude.toFixed(6) }}, {{ mapVisualizationData.endPoint.longitude.toFixed(6) }}</p>
          <p><strong>中心点:</strong> {{ mapVisualizationData.centerPoint.latitude.toFixed(6) }}, {{ mapVisualizationData.centerPoint.longitude.toFixed(6) }}</p>
          <p><strong>边界:</strong></p>
          <ul>
            <li>北: {{ mapVisualizationData.bounds.north.toFixed(6) }}</li>
            <li>南: {{ mapVisualizationData.bounds.south.toFixed(6) }}</li>
            <li>东: {{ mapVisualizationData.bounds.east.toFixed(6) }}</li>
            <li>西: {{ mapVisualizationData.bounds.west.toFixed(6) }}</li>
          </ul>
        </div>
      </div>

      <!-- 热力图数据 -->
      <div v-if="heatmapData" class="data-section">
        <h3>热力图数据 ({{ currentHeatmapType }})</h3>
        <div class="heatmap-controls">
          <button @click="loadHeatmap('altitude')" :class="{ active: currentHeatmapType === 'altitude' }">海拔</button>
          <button @click="loadHeatmap('heartrate')" :class="{ active: currentHeatmapType === 'heartrate' }">心率</button>
          <button @click="loadHeatmap('speed')" :class="{ active: currentHeatmapType === 'speed' }">速度</button>
        </div>
        <p><strong>数据点数量:</strong> {{ heatmapData.points.length }}</p>
        <div v-if="heatmapData.points.length > 0" class="heatmap-sample">
          <h4>前5个数据点示例:</h4>
          <div v-for="(point, index) in heatmapData.points.slice(0, 5)" :key="index" class="heatmap-point">
            <span>{{ point.latitude.toFixed(6) }}, {{ point.longitude.toFixed(6) }}</span>
            <span>值: {{ point.value.toFixed(2) }}</span>
            <span>标准化: {{ (point.normalizedValue * 100).toFixed(1) }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useDataLoader } from '@/composables/useDataLoader'

const {
  isLoading,
  error,
  extractedSensorData,
  mapVisualizationData,
  workoutSummary,
  loadDataWithApi,
  loadHeatmapData
} = useDataLoader()

const heatmapData = ref<any>(null)
const currentHeatmapType = ref<'altitude' | 'heartrate' | 'speed'>('altitude')

async function loadAllData() {
  await loadDataWithApi()
  // 默认加载海拔热力图
  await loadHeatmap('altitude')
}

async function loadSpecificData() {
  try {
    // 演示加载特定类型的数据
    const [altitudeData, heartRateData, speedData] = await Promise.all([
      loadDataWithApi(),
      loadHeatmapData('altitude'),
      loadHeatmapData('heartrate')
    ])
    
    console.log('特定数据加载完成:', {
      altitude: altitudeData,
      heartRate: heartRateData
    })
  } catch (err) {
    console.error('加载特定数据失败:', err)
  }
}

async function loadHeatmap(type: 'altitude' | 'heartrate' | 'speed') {
  try {
    currentHeatmapType.value = type
    heatmapData.value = await loadHeatmapData(type)
  } catch (err) {
    console.error(`加载${type}热力图失败:`, err)
  }
}

function clearData() {
  extractedSensorData.value = null
  mapVisualizationData.value = null
  workoutSummary.value = null
  heatmapData.value = null
  error.value = null
}
</script>

<style scoped>
.api-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.demo-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 30px;
}

.btn-primary, .btn-secondary, .btn-danger {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #7f8c8d;
}

.btn-danger {
  background-color: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background-color: #c0392b;
}

.btn-primary:disabled, .btn-secondary:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.data-section {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.data-section h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.summary-item label {
  font-weight: 600;
  color: #495057;
}

.sensor-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background-color: white;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  text-align: center;
}

.stat-card p {
  margin: 5px 0;
  display: flex;
  justify-content: space-between;
}

.map-stats ul {
  margin-left: 20px;
}

.heatmap-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.heatmap-controls button {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.heatmap-controls button:hover {
  background-color: #e9ecef;
}

.heatmap-controls button.active {
  background-color: #3498db;
  color: white;
  border-color: #3498db;
}

.heatmap-sample {
  margin-top: 15px;
}

.heatmap-point {
  display: flex;
  justify-content: space-between;
  padding: 5px 10px;
  background-color: white;
  margin: 5px 0;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  font-family: monospace;
  font-size: 0.9em;
}
</style>
