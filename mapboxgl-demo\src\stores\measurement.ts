import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export type MeasurementSystem = 'metric' | 'imperial'

// 单位系统管理
export const useMeasurementStore = defineStore('measurement', () => {
  // 状态
  const system = ref<MeasurementSystem>('metric')

  // 计算属性
  const distanceUnit = computed(() => system.value === 'metric' ? 'km' : 'mi')
  const speedUnit = computed(() => system.value === 'metric' ? 'km/h' : 'mph')
  const elevationUnit = computed(() => system.value === 'metric' ? 'm' : 'ft')
  const temperatureUnit = computed(() => system.value === 'metric' ? '°C' : '°F')

  // 转换函数
  const convertDistance = (meters: number): number => {
    if (system.value === 'metric') {
      return meters / 1000 // 转换为公里
    } else {
      return meters * 0.000621371 // 转换为英里
    }
  }

  const convertSpeed = (metersPerSecond: number): number => {
    if (system.value === 'metric') {
      return metersPerSecond * 3.6 // 转换为 km/h
    } else {
      return metersPerSecond * 2.23694 // 转换为 mph
    }
  }

  const convertElevation = (meters: number): number => {
    if (system.value === 'metric') {
      return meters
    } else {
      return meters * 3.28084 // 转换为英尺
    }
  }

  const convertTemperature = (celsius: number): number => {
    if (system.value === 'metric') {
      return celsius
    } else {
      return celsius * 9/5 + 32 // 转换为华氏度
    }
  }

  // 格式化函数
  const formatDistance = (meters: number, precision = 2): string => {
    const converted = convertDistance(meters)
    return `${converted.toFixed(precision)} ${distanceUnit.value}`
  }

  const formatSpeed = (metersPerSecond: number, precision = 1): string => {
    const converted = convertSpeed(metersPerSecond)
    return `${converted.toFixed(precision)} ${speedUnit.value}`
  }

  const formatElevation = (meters: number, precision = 0): string => {
    const converted = convertElevation(meters)
    return `${converted.toFixed(precision)} ${elevationUnit.value}`
  }

  const formatTemperature = (celsius: number, precision = 1): string => {
    const converted = convertTemperature(celsius)
    return `${converted.toFixed(precision)}${temperatureUnit.value}`
  }

  // 动作
  const setSystem = (newSystem: MeasurementSystem) => {
    system.value = newSystem
    // 保存到本地存储
    localStorage.setItem('measurement-system', newSystem)
  }

  const toggleSystem = () => {
    setSystem(system.value === 'metric' ? 'imperial' : 'metric')
  }

  // 初始化（从本地存储读取）
  const initializeFromStorage = () => {
    const stored = localStorage.getItem('measurement-system') as MeasurementSystem
    if (stored && ['metric', 'imperial'].includes(stored)) {
      system.value = stored
    }
  }

  return {
    // 状态
    system,
    
    // 计算属性
    distanceUnit,
    speedUnit,
    elevationUnit,
    temperatureUnit,
    
    // 转换函数
    convertDistance,
    convertSpeed,
    convertElevation,
    convertTemperature,
    
    // 格式化函数
    formatDistance,
    formatSpeed,
    formatElevation,
    formatTemperature,
    
    // 动作
    setSystem,
    toggleSystem,
    initializeFromStorage
  }
})
