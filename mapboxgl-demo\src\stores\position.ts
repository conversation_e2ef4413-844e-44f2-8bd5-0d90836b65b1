import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 播放状态管理（等同原项目的 PositionContext）
export const usePositionStore = defineStore('position', () => {
  // 状态
  const position = ref(0) // 0-1 播放进度
  const isPlaying = ref(false)
  const startTime = ref(0)
  const duration = ref(60000) // 默认60秒，毫秒
  const speed = ref(1) // 播放速度倍数

  // 计算属性
  const currentTime = computed(() => position.value * duration.value)
  const remainingTime = computed(() => duration.value - currentTime.value)
  const progressPercent = computed(() => Math.round(position.value * 100))

  // 动作
  const setPosition = (val: number) => {
    position.value = Math.max(0, Math.min(val, 1))
  }

  const setDuration = (val: number) => {
    duration.value = val
  }

  const setSpeed = (val: number) => {
    speed.value = Math.max(0.1, Math.min(val, 5))
  }

  const play = () => {
    if (!isPlaying.value) {
      isPlaying.value = true
      startTime.value = Date.now() / duration.value - position.value
    }
  }

  const pause = () => {
    isPlaying.value = false
  }

  const togglePlay = () => {
    if (isPlaying.value) {
      pause()
    } else {
      play()
    }
  }

  const reset = () => {
    position.value = 0
    isPlaying.value = false
    startTime.value = 0
  }

  const seekTo = (time: number) => {
    setPosition(time / duration.value)
  }

  // 更新播放进度（需要在动画循环中调用）
  const updatePosition = () => {
    if (isPlaying.value) {
      const elapsed = Date.now() / duration.value - startTime.value
      const newPosition = elapsed * speed.value
      
      if (newPosition >= 1) {
        position.value = 1
        isPlaying.value = false
      } else {
        position.value = newPosition
      }
    }
  }

  return {
    // 状态
    position,
    isPlaying,
    duration,
    speed,
    
    // 计算属性
    currentTime,
    remainingTime,
    progressPercent,
    
    // 动作
    setPosition,
    setDuration,
    setSpeed,
    play,
    pause,
    togglePlay,
    reset,
    seekTo,
    updatePosition
  }
})
