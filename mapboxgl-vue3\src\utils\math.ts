// 数学工具函数（复用原项目逻辑）

/**
 * 线性插值
 */
export const lerp = (a: number, b: number, t: number): number => {
  return a + (b - a) * t
}

/**
 * 将值限制在指定范围内
 */
export const clamp = (value: number, min: number, max: number): number => {
  return Math.min(Math.max(value, min), max)
}

/**
 * 将值从一个范围映射到另一个范围
 */
export const mapRange = (
  value: number,
  inMin: number,
  inMax: number,
  outMin: number,
  outMax: number
): number => {
  return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin
}

/**
 * 平滑步进函数
 */
export const smoothstep = (edge0: number, edge1: number, x: number): number => {
  const t = clamp((x - edge0) / (edge1 - edge0), 0, 1)
  return t * t * (3 - 2 * t)
}

/**
 * 计算两点之间的距离（2D）
 */
export const distance2D = (x1: number, y1: number, x2: number, y2: number): number => {
  const dx = x2 - x1
  const dy = y2 - y1
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * 计算两点之间的距离（3D）
 */
export const distance3D = (
  x1: number, y1: number, z1: number,
  x2: number, y2: number, z2: number
): number => {
  const dx = x2 - x1
  const dy = y2 - y1
  const dz = z2 - z1
  return Math.sqrt(dx * dx + dy * dy + dz * dz)
}

/**
 * 角度转弧度
 */
export const degToRad = (degrees: number): number => {
  return degrees * Math.PI / 180
}

/**
 * 弧度转角度
 */
export const radToDeg = (radians: number): number => {
  return radians * 180 / Math.PI
}

/**
 * 标准化角度到 0-360 度范围
 */
export const normalizeAngle = (angle: number): number => {
  angle = angle % 360
  return angle < 0 ? angle + 360 : angle
}

/**
 * 计算两个角度之间的最短差值
 */
export const angleDifference = (a: number, b: number): number => {
  const diff = normalizeAngle(b - a)
  return diff > 180 ? diff - 360 : diff
}

/**
 * 贝塞尔曲线插值
 */
export const bezier = (t: number, p0: number, p1: number, p2: number, p3: number): number => {
  const u = 1 - t
  const tt = t * t
  const uu = u * u
  const uuu = uu * u
  const ttt = tt * t

  return uuu * p0 + 3 * uu * t * p1 + 3 * u * tt * p2 + ttt * p3
}

/**
 * 缓动函数
 */
export const easing = {
  linear: (t: number) => t,
  easeInQuad: (t: number) => t * t,
  easeOutQuad: (t: number) => t * (2 - t),
  easeInOutQuad: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  easeInCubic: (t: number) => t * t * t,
  easeOutCubic: (t: number) => (--t) * t * t + 1,
  easeInOutCubic: (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
}

/**
 * 生成随机数
 */
export const random = (min: number = 0, max: number = 1): number => {
  return Math.random() * (max - min) + min
}

/**
 * 生成随机整数
 */
export const randomInt = (min: number, max: number): number => {
  return Math.floor(random(min, max + 1))
}

/**
 * 数组求和
 */
export const sum = (arr: number[]): number => {
  return arr.reduce((acc, val) => acc + val, 0)
}

/**
 * 数组平均值
 */
export const average = (arr: number[]): number => {
  return arr.length > 0 ? sum(arr) / arr.length : 0
}

/**
 * 数组最大值
 */
export const max = (arr: number[]): number => {
  return Math.max(...arr)
}

/**
 * 数组最小值
 */
export const min = (arr: number[]): number => {
  return Math.min(...arr)
}
