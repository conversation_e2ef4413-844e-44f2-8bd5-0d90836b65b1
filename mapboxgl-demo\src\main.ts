import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import { createI18n } from 'vue-i18n'

// 导入根组件
import App from './App.vue'

// 导入路由配置
import { routes } from './router'

// 导入国际化配置
import zh from './locales/zh.json'
import en from './locales/en.json'

// 创建Vue应用实例
const app = createApp(App)

// 创建Pinia状态管理
const pinia = createPinia()

// 创建路由
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 创建国际化
const i18n = createI18n({
  legacy: false, // 使用 Composition API 模式
  locale: 'zh',
  fallbackLocale: 'en',
  messages: {
    zh,
    en
  }
})

// 注册插件
app.use(pinia)
app.use(router)
app.use(i18n)

// 挂载应用
app.mount('#app') 