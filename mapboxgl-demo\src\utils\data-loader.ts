import type { Track, TrackPoint } from '@/types'

/**
 * 从CSV数据加载轨迹
 */
export const loadTrackFromCSV = async (csvPath: string): Promise<Track> => {
  try {
    const response = await fetch(csvPath)
    const csvText = await response.text()
    
    const lines = csvText.trim().split('\n')
    const headers = lines[0].split(',')
    
    const points: TrackPoint[] = []
    let totalDistance = 0
    let speeds: number[] = []
    let elevations: number[] = []
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',')
      
      const timestamp = new Date(values[0]).getTime()
      const lat = parseFloat(values[1])
      const lng = parseFloat(values[2])
      const elevation = parseFloat(values[3]) || 0
      const speed = parseFloat(values[4]) || 0
      
      // 跳过无效数据
      if (isNaN(lat) || isNaN(lng)) continue
      
      const point: TrackPoint = {
        lat,
        lng,
        elevation,
        time: timestamp,
        speed: speed > 0 ? speed : undefined
      }
      
      points.push(point)
      
      if (speed > 0) speeds.push(speed)
      elevations.push(elevation)
      
      // 计算距离（如果不是第一个点）
      if (points.length > 1) {
        const prevPoint = points[points.length - 2]
        totalDistance += calculateDistance(prevPoint, point)
      }
    }
    
    // 计算统计数据
    const startTime = points[0]?.time || Date.now()
    const endTime = points[points.length - 1]?.time || Date.now()
    const totalTime = endTime - startTime
    
    const validSpeeds = speeds.filter(s => s > 0)
    const maxSpeed = validSpeeds.length > 0 ? Math.max(...validSpeeds) : 0
    const avgSpeed = validSpeeds.length > 0 ? validSpeeds.reduce((a, b) => a + b, 0) / validSpeeds.length : 0
    
    const elevationGain = calculateElevationGain(elevations)
    const elevationLoss = calculateElevationLoss(elevations)
    const maxElevation = elevations.length > 0 ? Math.max(...elevations) : 0
    const minElevation = elevations.length > 0 ? Math.min(...elevations) : 0
    
    return {
      id: 'real-track-csv',
      name: '真实GPS轨迹',
      sport: 'running', // 可以根据数据特征推断
      points,
      startTime,
      endTime,
      totalDistance,
      totalTime,
      maxSpeed,
      avgSpeed,
      elevationGain,
      elevationLoss,
      maxElevation,
      minElevation
    }
  } catch (error) {
    console.error('加载CSV数据失败:', error)
    throw new Error('无法加载轨迹数据')
  }
}

/**
 * 从GeoJSON数据加载轨迹
 */
export const loadTrackFromGeoJSON = async (geojsonPath: string): Promise<Track> => {
  try {
    const response = await fetch(geojsonPath)
    const geojson = await response.json()
    
    if (geojson.type !== 'Feature' || geojson.geometry.type !== 'LineString') {
      throw new Error('不支持的GeoJSON格式')
    }
    
    const coordinates = geojson.geometry.coordinates
    const points: TrackPoint[] = []
    let totalDistance = 0
    let elevations: number[] = []
    
    // 假设时间间隔为5秒（可以根据实际情况调整）
    const timeInterval = 5000
    const startTime = Date.now() - coordinates.length * timeInterval
    
    for (let i = 0; i < coordinates.length; i++) {
      const [lng, lat, elevation = 0] = coordinates[i]
      
      const point: TrackPoint = {
        lat,
        lng,
        elevation,
        time: startTime + i * timeInterval
      }
      
      points.push(point)
      elevations.push(elevation)
      
      // 计算距离
      if (i > 0) {
        const prevPoint = points[i - 1]
        const distance = calculateDistance(prevPoint, point)
        totalDistance += distance
        
        // 计算速度 (m/s)
        const timeDiff = (point.time - prevPoint.time) / 1000
        if (timeDiff > 0) {
          point.speed = distance / timeDiff
        }
      }
    }
    
    // 计算统计数据
    const endTime = startTime + coordinates.length * timeInterval
    const totalTime = endTime - startTime
    
    const speeds = points.map(p => p.speed || 0).filter(s => s > 0)
    const maxSpeed = speeds.length > 0 ? Math.max(...speeds) : 0
    const avgSpeed = speeds.length > 0 ? speeds.reduce((a, b) => a + b, 0) / speeds.length : 0
    
    const elevationGain = calculateElevationGain(elevations)
    const elevationLoss = calculateElevationLoss(elevations)
    const maxElevation = Math.max(...elevations)
    const minElevation = Math.min(...elevations)
    
    return {
      id: 'real-track-geojson',
      name: geojson.properties?.name || '真实GPS轨迹',
      sport: 'running',
      points,
      startTime,
      endTime,
      totalDistance,
      totalTime,
      maxSpeed,
      avgSpeed,
      elevationGain,
      elevationLoss,
      maxElevation,
      minElevation
    }
  } catch (error) {
    console.error('加载GeoJSON数据失败:', error)
    throw new Error('无法加载轨迹数据')
  }
}

/**
 * 计算两点之间的距离（Haversine公式）
 */
const calculateDistance = (point1: { lat: number; lng: number }, point2: { lat: number; lng: number }): number => {
  const R = 6371000 // 地球半径（米）
  const dLat = (point2.lat - point1.lat) * Math.PI / 180
  const dLng = (point2.lng - point1.lng) * Math.PI / 180
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

/**
 * 计算爬升
 */
const calculateElevationGain = (elevations: number[]): number => {
  let gain = 0
  for (let i = 1; i < elevations.length; i++) {
    const diff = elevations[i] - elevations[i - 1]
    if (diff > 0) gain += diff
  }
  return gain
}

/**
 * 计算下降
 */
const calculateElevationLoss = (elevations: number[]): number => {
  let loss = 0
  for (let i = 1; i < elevations.length; i++) {
    const diff = elevations[i] - elevations[i - 1]
    if (diff < 0) loss += Math.abs(diff)
  }
  return loss
}

/**
 * 加载默认轨迹数据
 */
export const loadDefaultTrack = async (): Promise<Track> => {
  // 优先尝试加载CSV数据，如果失败则尝试GeoJSON
  try {
    return await loadTrackFromCSV('/mock_data/workout_track.csv')
  } catch (error) {
    console.warn('CSV加载失败，尝试GeoJSON:', error)
    try {
      return await loadTrackFromGeoJSON('/mock_data/workout_track.geojson')
    } catch (geoError) {
      console.error('所有数据格式加载失败:', geoError)
      throw new Error('无法加载任何轨迹数据')
    }
  }
}
