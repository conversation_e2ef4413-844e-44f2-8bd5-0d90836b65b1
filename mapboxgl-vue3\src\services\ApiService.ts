/**
 * API 服务 - 模拟后端 API 接口
 * 提供类似 REST API 的调用方式
 */

import { DataService } from './DataService'
import type { SportsTrackerResponse } from '@/types/api'
import type { ExtractedSensorData, MapVisualizationData } from '@/types/extensions'

export class ApiService {
  private dataService: DataService

  constructor() {
    this.dataService = DataService.getInstance()
  }

  /**
   * 模拟 API: GET /api/workouts/{id}
   * 获取完整的运动数据
   */
  async getWorkout(id: string = 'default'): Promise<{
    success: boolean
    data: SportsTrackerResponse
    message?: string
  }> {
    try {
      const data = await this.dataService.loadWorkoutResponse()
      return {
        success: true,
        data
      }
    } catch (error) {
      return {
        success: false,
        data: {} as SportsTrackerResponse,
        message: error instanceof Error ? error.message : '获取运动数据失败'
      }
    }
  }

  /**
   * 模拟 API: GET /api/workouts/{id}/summary
   * 获取运动摘要信息
   */
  async getWorkoutSummary(id: string = 'default') {
    try {
      const summary = await this.dataService.getWorkoutSummary()
      return {
        success: true,
        data: summary
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '获取运动摘要失败'
      }
    }
  }

  /**
   * 模拟 API: GET /api/workouts/{id}/track
   * 获取轨迹数据
   */
  async getTrackData(id: string = 'default') {
    try {
      const trackData = await this.dataService.getMapVisualizationData()
      return {
        success: true,
        data: trackData
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '获取轨迹数据失败'
      }
    }
  }

  /**
   * 模拟 API: GET /api/workouts/{id}/sensors
   * 获取传感器数据
   */
  async getSensorData(id: string = 'default') {
    try {
      const sensorData = await this.dataService.getExtractedSensorData()
      return {
        success: true,
        data: sensorData
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '获取传感器数据失败'
      }
    }
  }

  /**
   * 模拟 API: GET /api/workouts/{id}/altitude
   * 获取海拔数据
   */
  async getAltitudeData(id: string = 'default') {
    try {
      const sensorData = await this.dataService.getExtractedSensorData()
      return {
        success: true,
        data: {
          altitude: sensorData.altitude,
          minAltitude: sensorData.altitude.min,
          maxAltitude: sensorData.altitude.max,
          elevationProfile: sensorData.altitude.values
        }
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '获取海拔数据失败'
      }
    }
  }

  /**
   * 模拟 API: GET /api/workouts/{id}/heartrate
   * 获取心率数据
   */
  async getHeartRateData(id: string = 'default') {
    try {
      const sensorData = await this.dataService.getExtractedSensorData()
      return {
        success: true,
        data: {
          heartRate: sensorData.heartRate,
          zones: this.calculateHeartRateZones(sensorData.heartRate.values),
          timeInZones: this.calculateTimeInZones(sensorData.heartRate.values)
        }
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '获取心率数据失败'
      }
    }
  }

  /**
   * 模拟 API: GET /api/workouts/{id}/speed
   * 获取速度数据
   */
  async getSpeedData(id: string = 'default') {
    try {
      const sensorData = await this.dataService.getExtractedSensorData()
      return {
        success: true,
        data: {
          speed: sensorData.speed,
          paceData: this.convertSpeedToPace(sensorData.speed.values),
          speedZones: this.calculateSpeedZones(sensorData.speed.values)
        }
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '获取速度数据失败'
      }
    }
  }

  /**
   * 模拟 API: GET /api/workouts/{id}/heatmap/{type}
   * 获取热力图数据
   */
  async getHeatmapData(id: string = 'default', type: 'altitude' | 'heartrate' | 'speed' = 'altitude') {
    try {
      const mapData = await this.dataService.getMapVisualizationData()
      let heatmapData

      switch (type) {
        case 'altitude':
          heatmapData = mapData.altitudeHeatmap
          break
        case 'heartrate':
          heatmapData = mapData.heartRateHeatmap
          break
        case 'speed':
          heatmapData = mapData.speedHeatmap
          break
        default:
          heatmapData = mapData.altitudeHeatmap
      }

      return {
        success: true,
        data: {
          type,
          points: heatmapData,
          bounds: mapData.bounds,
          center: mapData.centerPoint
        }
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '获取热力图数据失败'
      }
    }
  }

  /**
   * 计算心率区间
   */
  private calculateHeartRateZones(heartRateValues: number[]) {
    if (heartRateValues.length === 0) return []

    const maxHR = Math.max(...heartRateValues)
    return [
      { zone: 1, range: [0, maxHR * 0.6], name: '恢复区间', color: '#4CAF50' },
      { zone: 2, range: [maxHR * 0.6, maxHR * 0.7], name: '有氧基础', color: '#2196F3' },
      { zone: 3, range: [maxHR * 0.7, maxHR * 0.8], name: '有氧强度', color: '#FF9800' },
      { zone: 4, range: [maxHR * 0.8, maxHR * 0.9], name: '乳酸阈值', color: '#FF5722' },
      { zone: 5, range: [maxHR * 0.9, maxHR], name: '无氧功率', color: '#F44336' }
    ]
  }

  /**
   * 计算各心率区间的时间分布
   */
  private calculateTimeInZones(heartRateValues: number[]) {
    const zones = this.calculateHeartRateZones(heartRateValues)
    const timeInZones = zones.map(zone => ({ ...zone, time: 0, percentage: 0 }))

    heartRateValues.forEach(hr => {
      const zone = zones.find(z => hr >= z.range[0] && hr < z.range[1])
      if (zone) {
        const zoneIndex = zone.zone - 1
        timeInZones[zoneIndex].time += 1 // 假设每个数据点代表1秒
      }
    })

    const totalTime = heartRateValues.length
    timeInZones.forEach(zone => {
      zone.percentage = totalTime > 0 ? (zone.time / totalTime) * 100 : 0
    })

    return timeInZones
  }

  /**
   * 将速度转换为配速
   */
  private convertSpeedToPace(speedValues: number[]) {
    return speedValues.map(speed => {
      if (speed <= 0) return 0
      return 1000 / (speed * 60) // 分钟/公里
    })
  }

  /**
   * 计算速度区间
   */
  private calculateSpeedZones(speedValues: number[]) {
    if (speedValues.length === 0) return []

    const maxSpeed = Math.max(...speedValues)
    return [
      { zone: 1, range: [0, maxSpeed * 0.5], name: '慢跑', color: '#4CAF50' },
      { zone: 2, range: [maxSpeed * 0.5, maxSpeed * 0.7], name: '轻松跑', color: '#2196F3' },
      { zone: 3, range: [maxSpeed * 0.7, maxSpeed * 0.85], name: '节奏跑', color: '#FF9800' },
      { zone: 4, range: [maxSpeed * 0.85, maxSpeed * 0.95], name: '阈值跑', color: '#FF5722' },
      { zone: 5, range: [maxSpeed * 0.95, maxSpeed], name: '冲刺', color: '#F44336' }
    ]
  }
}

// 导出单例实例
export const apiService = new ApiService()
