<template>
  <n-config-provider :theme="theme" :locale="locale" :date-locale="dateLocale">
    <n-global-style />
    <n-loading-bar-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <n-message-provider>
            <router-view />
          </n-message-provider>
        </n-notification-provider>
      </n-dialog-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  NConfigProvider, 
  NGlobalStyle, 
  NLoadingBarProvider,
  NDialogProvider,
  NNotificationProvider,
  NMessageProvider,
  darkTheme,
  zhCN,
  dateZhCN,
  enUS,
  dateEnUS
} from 'naive-ui'
import { useThemeStore } from '@/stores/theme'
import { useI18n } from 'vue-i18n'

// 主题状态管理
const themeStore = useThemeStore()
const { locale: currentLocale } = useI18n()

// 计算主题
const theme = computed(() => themeStore.isDark ? darkTheme : null)

// 计算语言设置
const locale = computed(() => currentLocale.value === 'zh' ? zhCN : enUS)
const dateLocale = computed(() => currentLocale.value === 'zh' ? dateZhCN : dateEnUS)
</script>

<style scoped>
/* 全局样式 */
body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* Mapbox GL 样式重置 */
.mapboxgl-map {
  font-family: inherit;
}

.mapboxgl-ctrl-group {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}
</style> 