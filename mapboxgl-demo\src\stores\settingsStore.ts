/**
 * 用户设置状态管理
 * 基于Suunto应用的测量系统和国际化设计
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  UserSettings, 
  MeasurementSystem, 
  LanguageCode, 
  Theme, 
  MapStyle 
} from '@/types/common'

// 支持的语言列表（基于Suunto的25种语言）
export const SUPPORTED_LANGUAGES: Record<LanguageCode, { label: string; nativeName: string }> = {
  'zh-CN': { label: 'Chinese (Simplified)', nativeName: '中文' },
  'en-US': { label: 'English (US)', nativeName: 'English' },
  'ja-JP': { label: 'Japanese', nativeName: '日本語' },
  'ko-KR': { label: 'Korean', nativeName: '한국어' },
  'fr-FR': { label: 'French', nativeName: 'Français' },
  'de-DE': { label: 'German', nativeName: 'Deutsch' },
  'es-ES': { label: 'Spanish', nativeName: 'Español' },
  'it-IT': { label: 'Italian', nativeName: 'Italiano' },
  'pt-PT': { label: 'Portuguese', nativeName: 'Português' },
  'ru-RU': { label: 'Russian', nativeName: 'Русский' },
  'ar-SA': { label: 'Arabic', nativeName: 'العربية' },
  'hi-IN': { label: 'Hindi', nativeName: 'हिन्दी' },
  'th-TH': { label: 'Thai', nativeName: 'ไทย' },
  'vi-VN': { label: 'Vietnamese', nativeName: 'Tiếng Việt' },
  'id-ID': { label: 'Indonesian', nativeName: 'Bahasa Indonesia' },
  'ms-MY': { label: 'Malay', nativeName: 'Bahasa Melayu' },
  'tl-PH': { label: 'Filipino', nativeName: 'Filipino' },
  'tr-TR': { label: 'Turkish', nativeName: 'Türkçe' },
  'pl-PL': { label: 'Polish', nativeName: 'Polski' },
  'nl-NL': { label: 'Dutch', nativeName: 'Nederlands' },
  'sv-SE': { label: 'Swedish', nativeName: 'Svenska' },
  'da-DK': { label: 'Danish', nativeName: 'Dansk' },
  'no-NO': { label: 'Norwegian', nativeName: 'Norsk' },
  'fi-FI': { label: 'Finnish', nativeName: 'Suomi' },
  'cs-CZ': { label: 'Czech', nativeName: 'Čeština' }
}

// 使用英制系统的国家/地区
const IMPERIAL_COUNTRIES = ['United States', 'Bahamas', 'Myanmar', 'Liberia']
const IMPERIAL_LOCALES = ['en-US']

export const useSettingsStore = defineStore('settings', () => {
  // 基础设置
  const language = ref<LanguageCode>('zh-CN')
  const measurementSystem = ref<MeasurementSystem>(MeasurementSystem.METRIC)
  const theme = ref<Theme>(Theme.AUTO)
  const mapStyle = ref<MapStyle>('streets' as MapStyle)
  
  // 功能开关
  const autoDetectLocation = ref(true)
  const enableAnalytics = ref(true)
  const enableExperimentalFeatures = ref(false)
  const enableDebugMode = ref(false)
  
  // 地图相关设置
  const defaultZoom = ref(10)
  const maxZoom = ref(20)
  const minZoom = ref(1)
  const enableTerrain = ref(true)
  const terrainExaggeration = ref(1.5)
  
  // 动画设置
  const enableAnimations = ref(true)
  const animationDuration = ref(1000)
  const enableSmoothTransitions = ref(true)
  
  // 性能设置
  const enableHighPerformance = ref(false)
  const maxFPS = ref(60)
  const enableWebGL2 = ref(true)
  
  // 计算属性
  const currentLanguageInfo = computed(() => SUPPORTED_LANGUAGES[language.value])
  
  const isMetricSystem = computed(() => measurementSystem.value === MeasurementSystem.METRIC)
  
  const isDarkTheme = computed(() => {
    if (theme.value === Theme.DARK) return true
    if (theme.value === Theme.LIGHT) return false
    // AUTO模式下检测系统主题
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  })
  
  const userSettings = computed<UserSettings>(() => ({
    language: language.value,
    measurementSystem: measurementSystem.value,
    theme: theme.value,
    mapStyle: mapStyle.value,
    autoDetectLocation: autoDetectLocation.value,
    enableAnalytics: enableAnalytics.value
  }))

  // Actions
  const setLanguage = (newLanguage: LanguageCode) => {
    if (SUPPORTED_LANGUAGES[newLanguage]) {
      language.value = newLanguage
      saveSettings()
      console.log(`🌐 语言切换为: ${SUPPORTED_LANGUAGES[newLanguage].nativeName}`)
    }
  }

  const setMeasurementSystem = (system: MeasurementSystem) => {
    measurementSystem.value = system
    saveSettings()
    console.log(`📏 测量系统切换为: ${system}`)
  }

  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
    saveSettings()
    console.log(`🎨 主题切换为: ${newTheme}`)
  }

  const setMapStyle = (style: MapStyle) => {
    mapStyle.value = style
    saveSettings()
  }

  const toggleMeasurementSystem = () => {
    const newSystem = measurementSystem.value === MeasurementSystem.METRIC 
      ? MeasurementSystem.IMPERIAL 
      : MeasurementSystem.METRIC
    setMeasurementSystem(newSystem)
  }

  const toggleTheme = () => {
    const newTheme = theme.value === Theme.LIGHT ? Theme.DARK : Theme.LIGHT
    setTheme(newTheme)
  }

  // 智能检测用户偏好（基于Suunto的检测逻辑）
  const detectUserPreferences = async () => {
    try {
      // 检测语言偏好
      const browserLanguage = navigator.language as LanguageCode
      if (SUPPORTED_LANGUAGES[browserLanguage]) {
        setLanguage(browserLanguage)
      } else {
        // 降级到语言代码的前两位
        const langCode = browserLanguage.split('-')[0]
        const fallbackLang = Object.keys(SUPPORTED_LANGUAGES).find(
          key => key.startsWith(langCode)
        ) as LanguageCode
        if (fallbackLang) {
          setLanguage(fallbackLang)
        }
      }

      // 检测测量系统偏好
      let detectedSystem = MeasurementSystem.METRIC
      
      // 优先使用地理位置检测
      if (autoDetectLocation.value) {
        try {
          const geoData = await detectUserCountry()
          if (geoData && IMPERIAL_COUNTRIES.includes(geoData.country)) {
            detectedSystem = MeasurementSystem.IMPERIAL
          }
        } catch (error) {
          console.warn('地理位置检测失败，使用语言降级策略')
        }
      }
      
      // 降级到语言检测
      if (detectedSystem === MeasurementSystem.METRIC && IMPERIAL_LOCALES.includes(browserLanguage)) {
        detectedSystem = MeasurementSystem.IMPERIAL
      }
      
      setMeasurementSystem(detectedSystem)

      // 检测主题偏好
      if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        setTheme(Theme.DARK)
      } else if (window.matchMedia('(prefers-color-scheme: light)').matches) {
        setTheme(Theme.LIGHT)
      }

      console.log('✅ 用户偏好检测完成')
    } catch (error) {
      console.error('❌ 用户偏好检测失败:', error)
    }
  }

  // 地理位置检测（模拟API调用）
  const detectUserCountry = async (): Promise<{ country: string } | null> => {
    try {
      // 这里应该调用实际的地理位置API
      // 例如：const response = await fetch('https://api.ipgeolocation.io/ipgeo?apiKey=YOUR_KEY')
      // 现在返回模拟数据
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ country: 'China' }) // 模拟返回中国
        }, 500)
      })
    } catch (error) {
      console.error('地理位置检测失败:', error)
      return null
    }
  }

  // 保存设置到本地存储
  const saveSettings = () => {
    try {
      const settings = {
        language: language.value,
        measurementSystem: measurementSystem.value,
        theme: theme.value,
        mapStyle: mapStyle.value,
        autoDetectLocation: autoDetectLocation.value,
        enableAnalytics: enableAnalytics.value,
        enableExperimentalFeatures: enableExperimentalFeatures.value,
        enableDebugMode: enableDebugMode.value,
        defaultZoom: defaultZoom.value,
        enableTerrain: enableTerrain.value,
        terrainExaggeration: terrainExaggeration.value,
        enableAnimations: enableAnimations.value,
        animationDuration: animationDuration.value,
        enableHighPerformance: enableHighPerformance.value,
        maxFPS: maxFPS.value
      }
      localStorage.setItem('mapboxgl-better-settings', JSON.stringify(settings))
    } catch (error) {
      console.error('保存设置失败:', error)
    }
  }

  // 从本地存储加载设置
  const loadSettings = () => {
    try {
      const saved = localStorage.getItem('mapboxgl-better-settings')
      if (saved) {
        const settings = JSON.parse(saved)
        
        if (settings.language && SUPPORTED_LANGUAGES[settings.language]) {
          language.value = settings.language
        }
        if (settings.measurementSystem) {
          measurementSystem.value = settings.measurementSystem
        }
        if (settings.theme) {
          theme.value = settings.theme
        }
        if (settings.mapStyle) {
          mapStyle.value = settings.mapStyle
        }
        if (typeof settings.autoDetectLocation === 'boolean') {
          autoDetectLocation.value = settings.autoDetectLocation
        }
        if (typeof settings.enableAnalytics === 'boolean') {
          enableAnalytics.value = settings.enableAnalytics
        }
        if (typeof settings.enableExperimentalFeatures === 'boolean') {
          enableExperimentalFeatures.value = settings.enableExperimentalFeatures
        }
        if (typeof settings.enableDebugMode === 'boolean') {
          enableDebugMode.value = settings.enableDebugMode
        }
        if (typeof settings.defaultZoom === 'number') {
          defaultZoom.value = settings.defaultZoom
        }
        if (typeof settings.enableTerrain === 'boolean') {
          enableTerrain.value = settings.enableTerrain
        }
        if (typeof settings.terrainExaggeration === 'number') {
          terrainExaggeration.value = settings.terrainExaggeration
        }
        if (typeof settings.enableAnimations === 'boolean') {
          enableAnimations.value = settings.enableAnimations
        }
        if (typeof settings.animationDuration === 'number') {
          animationDuration.value = settings.animationDuration
        }
        if (typeof settings.enableHighPerformance === 'boolean') {
          enableHighPerformance.value = settings.enableHighPerformance
        }
        if (typeof settings.maxFPS === 'number') {
          maxFPS.value = settings.maxFPS
        }

        console.log('✅ 设置加载完成')
      }
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }

  // 重置设置
  const resetSettings = () => {
    language.value = 'zh-CN'
    measurementSystem.value = MeasurementSystem.METRIC
    theme.value = Theme.AUTO
    mapStyle.value = 'streets' as MapStyle
    autoDetectLocation.value = true
    enableAnalytics.value = true
    enableExperimentalFeatures.value = false
    enableDebugMode.value = false
    defaultZoom.value = 10
    enableTerrain.value = true
    terrainExaggeration.value = 1.5
    enableAnimations.value = true
    animationDuration.value = 1000
    enableHighPerformance.value = false
    maxFPS.value = 60
    
    saveSettings()
    console.log('🔄 设置已重置')
  }

  // 初始化设置
  const initializeSettings = async () => {
    loadSettings()
    
    // 如果是首次使用，进行智能检测
    const isFirstTime = !localStorage.getItem('mapboxgl-better-settings')
    if (isFirstTime) {
      await detectUserPreferences()
    }
  }

  return {
    // State
    language,
    measurementSystem,
    theme,
    mapStyle,
    autoDetectLocation,
    enableAnalytics,
    enableExperimentalFeatures,
    enableDebugMode,
    defaultZoom,
    maxZoom,
    minZoom,
    enableTerrain,
    terrainExaggeration,
    enableAnimations,
    animationDuration,
    enableSmoothTransitions,
    enableHighPerformance,
    maxFPS,
    enableWebGL2,

    // Computed
    currentLanguageInfo,
    isMetricSystem,
    isDarkTheme,
    userSettings,

    // Actions
    setLanguage,
    setMeasurementSystem,
    setTheme,
    setMapStyle,
    toggleMeasurementSystem,
    toggleTheme,
    detectUserPreferences,
    saveSettings,
    loadSettings,
    resetSettings,
    initializeSettings,

    // Constants
    SUPPORTED_LANGUAGES
  }
})
