<template>
  <div class="info-panel">
    <TitleCard />
    <DataCard />
  </div>
</template>

<script setup lang="ts">
import TitleCard from './TitleCard.vue'
import DataCard from './DataCard.vue'
</script>

<style scoped>
.info-panel {
  width: 375px;
  min-width: 375px;
  background-color: #FFFFFF;
  border-right: 1px solid #E0E0E0;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  height: 90vh;

}
</style>
