<template>
  <div class="page-container">
    <div class="main-content">
      <InfoPanel />
      <MapPanel />
    </div>
    <ControlPanel />

    <!-- 地形控制面板 -->
    <div class="terrain-panel">
      <TerrainControls />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import TerrainControls from '@/components/TerrainControls.vue'

onMounted(() => {
  console.log('MapView mounted with terrain controls')
})
</script>

<style scoped>
.page-container {
  width: 100vw;
  height: 100vh;
  background-color: #F5F5F5;
  position: relative;
  overflow: hidden;
}

.main-content {
  display: flex;
  width: 100%;
  height: 90vh;
  gap: 0;
  padding: 0;
  box-sizing: border-box;
}
</style>
