# Sports Tracker API 使用指南

基于 `response.json` 数据的模拟 API 服务，提供完整的运动数据访问接口。

## 📋 API 概览

### 核心服务类
- **DataService**: 底层数据服务，直接处理 response.json
- **ApiService**: 高级 API 服务，提供 REST 风格的接口
- **useDataLoader**: Vue 组合式 API，响应式数据加载

## 🚀 快速开始

### 1. 基础用法

```typescript
import { apiService } from '@/services/ApiService'

// 获取完整运动数据
const workoutResult = await apiService.getWorkout()
if (workoutResult.success) {
  console.log('运动数据:', workoutResult.data)
}

// 获取运动摘要
const summaryResult = await apiService.getWorkoutSummary()
if (summaryResult.success) {
  console.log('运动摘要:', summaryResult.data)
}
```

### 2. 在 Vue 组件中使用

```vue
<script setup lang="ts">
import { useDataLoader } from '@/composables/useDataLoader'

const {
  isLoading,
  error,
  workoutSummary,
  extractedSensorData,
  mapVisualizationData,
  loadDataWithApi
} = useDataLoader()

// 加载数据
await loadDataWithApi()
</script>
```

## 📊 API 接口详解

### 1. 运动数据接口

#### `getWorkout(id?: string)`
获取完整的 Sports Tracker 响应数据

```typescript
const result = await apiService.getWorkout('workout-id')
// 返回: { success: boolean, data: SportsTrackerResponse, message?: string }
```

**返回数据结构:**
- `version`: 数据格式版本
- `camera`: 3D 相机配置
- `workout`: 运动核心数据
- `extensions`: 扩展数据（心率、海拔、位置等）
- `achievements`: 成就信息
- `tss`: 训练压力评分

#### `getWorkoutSummary(id?: string)`
获取运动摘要信息

```typescript
const result = await apiService.getWorkoutSummary()
// 返回运动的关键统计信息
```

**摘要包含:**
- 基本信息：运动员、类型、时间、持续时间
- 距离和速度：总距离、平均速度、最大速度、配速
- 高程：上升、下降、最小/最大海拔
- 心率：平均、最大心率
- 其他：卡路里、步数、步频、恢复时间

### 2. 轨迹数据接口

#### `getTrackData(id?: string)`
获取地图可视化数据

```typescript
const result = await apiService.getTrackData()
```

**返回数据:**
- `trackPoints`: GPS 轨迹点数组
- `startPoint/endPoint`: 起点/终点坐标
- `centerPoint`: 轨迹中心点
- `bounds`: 轨迹边界框
- `altitudeHeatmap/heartRateHeatmap/speedHeatmap`: 各类热力图数据

### 3. 传感器数据接口

#### `getSensorData(id?: string)`
获取所有传感器数据

```typescript
const result = await apiService.getSensorData()
```

**返回数据结构:**
```typescript
{
  altitude: TimeSeriesData,    // 海拔数据
  heartRate: TimeSeriesData,   // 心率数据
  speed: TimeSeriesData,       // 速度数据
  location: LocationPoint[]    // 位置数据
}
```

#### `getAltitudeData(id?: string)`
获取海拔数据

```typescript
const result = await apiService.getAltitudeData()
```

#### `getHeartRateData(id?: string)`
获取心率数据，包含区间分析

```typescript
const result = await apiService.getHeartRateData()
```

**返回数据:**
- `heartRate`: 心率时间序列
- `zones`: 心率区间定义
- `timeInZones`: 各区间时间分布

#### `getSpeedData(id?: string)`
获取速度数据

```typescript
const result = await apiService.getSpeedData()
```

**返回数据:**
- `speed`: 速度时间序列
- `paceData`: 配速数据
- `speedZones`: 速度区间

### 4. 热力图数据接口

#### `getHeatmapData(id?: string, type: 'altitude' | 'heartrate' | 'speed')`
获取指定类型的热力图数据

```typescript
// 获取海拔热力图
const altitudeHeatmap = await apiService.getHeatmapData('default', 'altitude')

// 获取心率热力图
const heartRateHeatmap = await apiService.getHeatmapData('default', 'heartrate')

// 获取速度热力图
const speedHeatmap = await apiService.getHeatmapData('default', 'speed')
```

**热力图数据结构:**
```typescript
{
  type: string,
  points: HeatmapPoint[],  // 热力图点数据
  bounds: BoundingBox,     // 边界框
  center: CenterPoint      // 中心点
}
```

## 🎯 实际应用示例

### 1. 地图轨迹渲染

```typescript
// 获取轨迹数据
const trackResult = await apiService.getTrackData()
if (trackResult.success) {
  const { trackPoints, bounds } = trackResult.data
  
  // 在地图上绘制轨迹
  map.addSource('track', {
    type: 'geojson',
    data: {
      type: 'Feature',
      geometry: {
        type: 'LineString',
        coordinates: trackPoints.map(p => [p.longitude, p.latitude])
      }
    }
  })
}
```

### 2. 心率区间分析

```typescript
// 获取心率数据
const hrResult = await apiService.getHeartRateData()
if (hrResult.success) {
  const { zones, timeInZones } = hrResult.data
  
  // 显示各区间时间分布
  timeInZones.forEach(zone => {
    console.log(`${zone.name}: ${zone.percentage.toFixed(1)}%`)
  })
}
```

### 3. 海拔热力图

```typescript
// 获取海拔热力图数据
const heatmapResult = await apiService.getHeatmapData('default', 'altitude')
if (heatmapResult.success) {
  const { points } = heatmapResult.data
  
  // 创建热力图图层
  points.forEach(point => {
    const color = getColorByValue(point.normalizedValue)
    // 在地图上添加颜色点
  })
}
```

## 🔧 高级用法

### 1. 使用 useDataLoader 组合式 API

```vue
<script setup lang="ts">
import { useDataLoader } from '@/composables/useDataLoader'

const {
  isLoading,
  error,
  workoutSummary,
  extractedSensorData,
  mapVisualizationData,
  loadDataWithApi,
  loadHeatmapData,
  loadHeartRateData
} = useDataLoader()

// 响应式加载数据
const loadData = async () => {
  await loadDataWithApi()
  
  // 加载特定类型数据
  const heartRateData = await loadHeartRateData()
  const altitudeHeatmap = await loadHeatmapData('altitude')
}
</script>

<template>
  <div v-if="isLoading">加载中...</div>
  <div v-else-if="error">错误: {{ error }}</div>
  <div v-else-if="workoutSummary">
    <h3>{{ workoutSummary.athlete }} 的{{ workoutSummary.activityType }}</h3>
    <p>距离: {{ workoutSummary.distance }}</p>
    <p>时间: {{ workoutSummary.duration }}</p>
  </div>
</template>
```

### 2. 数据缓存

所有 API 调用都自动缓存，避免重复请求：

```typescript
// 第一次调用会从文件加载
const result1 = await apiService.getWorkout()

// 第二次调用直接从缓存返回
const result2 = await apiService.getWorkout()

// 清除缓存
dataService.clearCache()
```

## 📝 注意事项

1. **数据来源**: 所有数据来自 `public/mock-data/response.json`
2. **异步操作**: 所有 API 调用都是异步的，需要使用 `await`
3. **错误处理**: API 返回统一的 `{ success, data, message }` 格式
4. **类型安全**: 完整的 TypeScript 类型定义
5. **响应式**: 配合 Vue 3 组合式 API 使用

## 🎨 可视化建议

基于提供的数据，推荐实现以下可视化：

1. **3D 轨迹地图**: 使用 `trackPoints` 和 `altitudeHeatmap`
2. **心率区间图**: 使用 `heartRateData` 的区间分析
3. **海拔剖面图**: 使用 `altitude` 时间序列数据
4. **速度变化图**: 使用 `speed` 时间序列数据
5. **热力图叠加**: 在地图上叠加不同类型的热力图

这套 API 设计完全基于您的 `response.json` 数据结构，提供了灵活且类型安全的数据访问方式。
