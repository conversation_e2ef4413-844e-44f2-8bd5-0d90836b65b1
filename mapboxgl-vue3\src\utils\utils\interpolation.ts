import type { TrackPoint } from '@/types'

/**
 * 轨迹插值工具
 * 参照 maps.suunto.com 的插值算法实现平滑的轨迹步进
 */

/**
 * 线性插值
 */
export const lerp = (a: number, b: number, t: number): number => {
  return a + (b - a) * t
}

/**
 * 平滑步进函数
 * 参照原项目的 smoothstep 实现
 */
export const smoothstep = (edge0: number, edge1: number, x: number): number => {
  const t = Math.max(0, Math.min(1, (x - edge0) / (edge1 - edge0)))
  return t * t * (3 - 2 * t)
}

/**
 * 角度插值（处理角度环绕）
 */
export const lerpAngle = (a: number, b: number, t: number): number => {
  const diff = ((b - a + 540) % 360) - 180
  return a + diff * t
}

/**
 * 将值从一个范围映射到另一个范围
 * 参照原项目的 scale 函数
 */
export const scale = (
  value: number,
  inputRange: [number, number],
  outputRange: [number, number]
): number => {
  const [inMin, inMax] = inputRange
  const [outMin, outMax] = outputRange
  return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin
}

/**
 * 轨迹进度因子
 * 参照原项目的 TRACK_PROGRESS_FACTOR
 */
export const TRACK_PROGRESS_FACTOR = 1.0

/**
 * 根据轨迹进度计算插值位置
 * 参照原项目的 getProgressMarkerPositionByLineString 方法
 */
export const getInterpolatedPosition = (
  trackPoints: TrackPoint[],
  progress: number
): {
  position: { lat: number; lng: number; elevation?: number }
  distance: number
  pointIndex: number
  interpolationFactor: number
} => {
  if (!trackPoints.length) {
    return {
      position: { lat: 0, lng: 0 },
      distance: 0,
      pointIndex: 0,
      interpolationFactor: 0
    }
  }

  // 计算总距离
  let totalDistance = 0
  const distances: number[] = [0]
  
  for (let i = 1; i < trackPoints.length; i++) {
    const prev = trackPoints[i - 1]
    const curr = trackPoints[i]
    const segmentDistance = calculateDistance(prev, curr)
    totalDistance += segmentDistance
    distances.push(totalDistance)
  }

  // 计算轨迹进度（0-1）
  const trackProgress = Math.min(1.0, TRACK_PROGRESS_FACTOR * progress)
  
  // 将进度转换为距离
  const targetDistance = scale(trackProgress, [0, 1], [0, totalDistance])

  // 在轨迹点中查找当前位置
  let prevPoint = trackPoints[0]
  let prevDistance = 0

  for (let i = 1; i < trackPoints.length; i++) {
    const currentPoint = trackPoints[i]
    const currentDistance = distances[i]

    if (currentDistance >= targetDistance) {
      // 在两个轨迹点之间进行线性插值
      const segmentLength = currentDistance - prevDistance
      const positionInSegment = segmentLength > 0 ? (targetDistance - prevDistance) / segmentLength : 0
      
      // 使用平滑插值
      const smoothT = smoothstep(0, 1, positionInSegment)

      return {
        position: {
          lat: lerp(prevPoint.lat, currentPoint.lat, smoothT),
          lng: lerp(prevPoint.lng, currentPoint.lng, smoothT),
          elevation: prevPoint.elevation && currentPoint.elevation 
            ? lerp(prevPoint.elevation, currentPoint.elevation, smoothT)
            : undefined
        },
        distance: targetDistance,
        pointIndex: i - 1,
        interpolationFactor: smoothT
      }
    }

    prevPoint = currentPoint
    prevDistance = currentDistance
  }

  // 如果超出轨迹范围，返回最后一个点
  const lastPoint = trackPoints[trackPoints.length - 1]
  return {
    position: {
      lat: lastPoint.lat,
      lng: lastPoint.lng,
      elevation: lastPoint.elevation
    },
    distance: totalDistance,
    pointIndex: trackPoints.length - 1,
    interpolationFactor: 1
  }
}

/**
 * 计算两点间距离（公里）
 */
const calculateDistance = (point1: TrackPoint, point2: TrackPoint): number => {
  const R = 6371 // 地球半径（公里）
  const dLat = (point2.lat - point1.lat) * Math.PI / 180
  const dLng = (point2.lng - point1.lng) * Math.PI / 180
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

/**
 * 批量计算轨迹点的累积距离
 * 用于 WebGL LineLayer 的数据预处理
 */
export const calculateCumulativeDistances = (trackPoints: TrackPoint[]): number[] => {
  const distances: number[] = [0]
  let cumulativeDistance = 0

  for (let i = 1; i < trackPoints.length; i++) {
    const prev = trackPoints[i - 1]
    const curr = trackPoints[i]
    const segmentDistance = calculateDistance(prev, curr)
    cumulativeDistance += segmentDistance
    distances.push(cumulativeDistance)
  }

  return distances
}

/**
 * 获取指定进度的距离值
 * 用于 WebGL 着色器的 uDist uniform
 */
export const getDistanceAtProgress = (
  trackPoints: TrackPoint[],
  progress: number
): number => {
  const interpolated = getInterpolatedPosition(trackPoints, progress)
  return interpolated.distance
}
