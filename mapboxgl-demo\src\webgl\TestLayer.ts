import type mapboxgl from 'mapbox-gl'

/**
 * 简单的测试图层，用于验证 CustomLayer 是否工作
 */
export class TestLayer implements mapboxgl.CustomLayerInterface {
  id: string
  type = 'custom' as const
  renderingMode = '3d' as const

  private program?: WebGLProgram

  constructor(id: string) {
    this.id = id
  }

  onAdd(map: mapboxgl.Map, gl: WebGLRenderingContext): void {
    // 简单的顶点着色器
    const vertexShaderSource = `
      attribute vec2 a_pos;
      uniform mat4 u_matrix;
      
      void main() {
        gl_Position = u_matrix * vec4(a_pos, 0.0, 1.0);
      }
    `

    // 简单的片段着色器
    const fragmentShaderSource = `
      precision mediump float;
      
      void main() {
        gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0); // 纯红色
      }
    `

    // 创建着色器
    const vertexShader = this.createShader(gl, gl.VERTEX_SHADER, vertexShaderSource)!
    const fragmentShader = this.createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource)!

    // 创建程序
    this.program = gl.createProgram()!
    gl.attachShader(this.program, vertexShader)
    gl.attachShader(this.program, fragmentShader)
    gl.linkProgram(this.program)

    if (!gl.getProgramParameter(this.program, gl.LINK_STATUS)) {
      console.error('程序链接失败:', gl.getProgramInfoLog(this.program))
      return
    }

    console.log('TestLayer 初始化完成')
  }

  render(gl: WebGLRenderingContext, matrix: number[]): void {
    if (!this.program) return

    gl.useProgram(this.program)

    // 使用轨迹的实际地理坐标 (法国阿尔卑斯山区域)
    // 经度: 6.8, 纬度: 45.9 (大致轨迹中心)
    const lng = 6.8
    const lat = 45.9

    // 转换为 Web Mercator 坐标
    const x = (lng + 180) / 360
    const y = (1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2

    // 创建一个在轨迹位置的三角形
    const size = 0.001  // 小一点的三角形
    const vertices = new Float32Array([
      x, y + size,        // 顶点
      x - size, y - size, // 左下
      x + size, y - size  // 右下
    ])

    const buffer = gl.createBuffer()
    gl.bindBuffer(gl.ARRAY_BUFFER, buffer)
    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW)

    const positionLocation = gl.getAttribLocation(this.program, 'a_pos')
    const matrixLocation = gl.getUniformLocation(this.program, 'u_matrix')

    gl.enableVertexAttribArray(positionLocation)
    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0)
    gl.uniformMatrix4fv(matrixLocation, false, matrix)

    // 禁用深度测试
    gl.disable(gl.DEPTH_TEST)

    // 绘制三角形
    gl.drawArrays(gl.TRIANGLES, 0, 3)

    // 减少日志输出
    if (Math.random() < 0.01) {
      console.log('TestLayer 渲染完成 - 坐标:', { x, y, lng, lat })
    }
  }

  onRemove(): void {
    console.log('TestLayer 已移除')
  }

  private createShader(gl: WebGLRenderingContext, type: number, source: string): WebGLShader | null {
    const shader = gl.createShader(type)!
    gl.shaderSource(shader, source)
    gl.compileShader(shader)

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      console.error('着色器编译失败:', gl.getShaderInfoLog(shader))
      gl.deleteShader(shader)
      return null
    }

    return shader
  }
}
