/**
 * 国际化配置
 * 基于Vue I18n，支持25种语言
 */

import type { I18nOptions } from 'vue-i18n'

// 导入语言包
import zhCN from './locales/zh-CN.json'
import enUS from './locales/en-US.json'
import jaJP from './locales/ja-JP.json'

// 检测浏览器语言
export const detectLanguage = (): string => {
  const browserLang = navigator.language
  const supportedLangs = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR', 'fr-FR', 'de-DE', 'es-ES', 'it-IT']
  
  // 精确匹配
  if (supportedLangs.includes(browserLang)) {
    return browserLang
  }
  
  // 语言代码匹配
  const langCode = browserLang.split('-')[0]
  const fallback = supportedLangs.find(lang => lang.startsWith(langCode))
  
  return fallback || 'zh-CN'
}

// i18n配置
export const i18nConfig: I18nOptions = {
  locale: detectLanguage(),
  fallbackLocale: 'en-US',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS,
    'ja-JP': jaJP
  },
  legacy: false,
  globalInjection: true,
  missingWarn: false,
  fallbackWarn: false
}

// 动态加载语言包
export const loadLanguageAsync = async (locale: string) => {
  try {
    const messages = await import(`./locales/${locale}.json`)
    return messages.default
  } catch (error) {
    console.error(`Failed to load language pack: ${locale}`, error)
    return null
  }
}

// 语言包懒加载映射
export const languageLoaders: Record<string, () => Promise<any>> = {
  'ko-KR': () => import('./locales/ko-KR.json'),
  'fr-FR': () => import('./locales/fr-FR.json'),
  'de-DE': () => import('./locales/de-DE.json'),
  'es-ES': () => import('./locales/es-ES.json'),
  'it-IT': () => import('./locales/it-IT.json'),
  'pt-PT': () => import('./locales/pt-PT.json'),
  'ru-RU': () => import('./locales/ru-RU.json'),
  'ar-SA': () => import('./locales/ar-SA.json'),
  'hi-IN': () => import('./locales/hi-IN.json'),
  'th-TH': () => import('./locales/th-TH.json'),
  'vi-VN': () => import('./locales/vi-VN.json'),
  'id-ID': () => import('./locales/id-ID.json'),
  'ms-MY': () => import('./locales/ms-MY.json'),
  'tl-PH': () => import('./locales/tl-PH.json'),
  'tr-TR': () => import('./locales/tr-TR.json'),
  'pl-PL': () => import('./locales/pl-PL.json'),
  'nl-NL': () => import('./locales/nl-NL.json'),
  'sv-SE': () => import('./locales/sv-SE.json'),
  'da-DK': () => import('./locales/da-DK.json'),
  'no-NO': () => import('./locales/no-NO.json'),
  'fi-FI': () => import('./locales/fi-FI.json'),
  'cs-CZ': () => import('./locales/cs-CZ.json')
}
