import mapboxgl, { type Map } from 'mapbox-gl'
import type { TrackPoint } from '@/types'

/**
 * 动态轨迹图层 - 基于进度逐步绘制轨迹线，支持颜色渐变
 * 参照 optimized-track-animation.html 的实现
 */
export class DynamicTrackLayer {
  private map: Map
  private trackPoints: TrackPoint[] = []
  private layerId: string
  private sourceId: string
  private currentProgress: number = 0
  private minElevation: number = 0
  private maxElevation: number = 0

  // 颜色配置 - 基于高度的渐变
  private readonly COLOR_CONFIG = {
    HIGH_COLOR: [255, 10, 0],    // 最高点：红色
    LOW_COLOR: [255, 235, 0]     // 最低点：黄色
  }

  constructor(map: Map, layerId: string = 'dynamic-track') {
    this.map = map
    this.layerId = layerId
    this.sourceId = `${layerId}-source`
  }

  /**
   * 设置轨迹数据
   */
  setTrackPoints(trackPoints: TrackPoint[]): void {
    this.trackPoints = trackPoints
    this.calculateElevationRange()
    this.createTrackSources()
    this.createTrackLayers()
    
    console.log('动态轨迹图层初始化完成:', {
      points: this.trackPoints.length,
      elevationRange: `${this.minElevation.toFixed(1)}m - ${this.maxElevation.toFixed(1)}m`,
      layerId: this.layerId
    })
  }

  /**
   * 计算高度范围用于颜色渐变
   */
  private calculateElevationRange(): void {
    if (this.trackPoints.length === 0) return

    this.minElevation = Math.min(...this.trackPoints.map(p => p.elevation || 0))
    this.maxElevation = Math.max(...this.trackPoints.map(p => p.elevation || 0))
  }

  /**
   * 生成平滑的轨迹线坐标 - 参照原始实现
   */
  private generateSmoothTrackCoordinates(progress: number): number[][] {
    if (this.trackPoints.length === 0) return []
    if (progress <= 0) return [[this.trackPoints[0].lng, this.trackPoints[0].lat]]

    const totalPoints = this.trackPoints.length
    const exactIndex = progress * (totalPoints - 1)
    const currentPointIndex = Math.floor(exactIndex)

    // 获取到当前点的所有轨迹点
    const visiblePoints = this.trackPoints.slice(0, Math.max(1, currentPointIndex + 1))
    let coordinates = visiblePoints.map(point => [point.lng, point.lat])

    // 如果有插值，添加插值点作为轨迹线的终点
    if (currentPointIndex < totalPoints - 1 && exactIndex > currentPointIndex) {
      const interpolatedPosition = this.getInterpolatedPosition(progress)
      if (interpolatedPosition) {
        // 替换最后一个点为插值点，实现平滑过渡
        coordinates[coordinates.length - 1] = [interpolatedPosition.lng, interpolatedPosition.lat]
      }
    }

    return coordinates
  }

  /**
   * 获取插值位置 - 基于进度计算精确的经纬度和高度
   */
  private getInterpolatedPosition(progress: number): TrackPoint | null {
    if (this.trackPoints.length === 0) return null
    if (progress <= 0) return this.trackPoints[0]
    if (progress >= 1) return this.trackPoints[this.trackPoints.length - 1]

    // 计算插值位置
    const totalPoints = this.trackPoints.length
    const exactIndex = progress * (totalPoints - 1)
    const lowerIndex = Math.floor(exactIndex)
    const upperIndex = Math.min(lowerIndex + 1, totalPoints - 1)
    const interpolationFactor = exactIndex - lowerIndex

    if (lowerIndex === upperIndex) {
      return this.trackPoints[lowerIndex]
    }

    const lowerPoint = this.trackPoints[lowerIndex]
    const upperPoint = this.trackPoints[upperIndex]

    // 线性插值计算经纬度和高度
    const interpolatedLng = lowerPoint.lng + (upperPoint.lng - lowerPoint.lng) * interpolationFactor
    const interpolatedLat = lowerPoint.lat + (upperPoint.lat - lowerPoint.lat) * interpolationFactor
    const interpolatedElevation = (lowerPoint.elevation || 0) + ((upperPoint.elevation || 0) - (lowerPoint.elevation || 0)) * interpolationFactor
    const interpolatedTime = lowerPoint.time + (upperPoint.time - lowerPoint.time) * interpolationFactor

    return {
      lng: interpolatedLng,
      lat: interpolatedLat,
      elevation: interpolatedElevation,
      time: interpolatedTime
    }
  }

  /**
   * 根据高度计算颜色
   */
  private getColorByElevation(elevation: number): string {
    if (this.minElevation === this.maxElevation) {
      return `rgb(${this.COLOR_CONFIG.HIGH_COLOR.join(',')})`
    }

    // 计算高度比例 (0-1)
    const ratio = (elevation - this.minElevation) / (this.maxElevation - this.minElevation)

    // 线性插值计算RGB值
    const r = Math.round(this.COLOR_CONFIG.LOW_COLOR[0] + (this.COLOR_CONFIG.HIGH_COLOR[0] - this.COLOR_CONFIG.LOW_COLOR[0]) * ratio)
    const g = Math.round(this.COLOR_CONFIG.LOW_COLOR[1] + (this.COLOR_CONFIG.HIGH_COLOR[1] - this.COLOR_CONFIG.LOW_COLOR[1]) * ratio)
    const b = Math.round(this.COLOR_CONFIG.LOW_COLOR[2] + (this.COLOR_CONFIG.HIGH_COLOR[2] - this.COLOR_CONFIG.LOW_COLOR[2]) * ratio)

    return `rgb(${r}, ${g}, ${b})`
  }

  /**
   * 创建轨迹数据源
   */
  private createTrackSources(): void {
    this.clearExistingSources()

    // 动态轨迹线数据源（初始为空）
    this.map.addSource(this.sourceId, {
      type: 'geojson',
      data: {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates: []
        }
      }
    })

    // 当前位置点数据源
    if (this.trackPoints.length > 0) {
      const firstPoint = this.trackPoints[0]
      this.map.addSource(`${this.sourceId}-current`, {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'Point',
            coordinates: [firstPoint.lng, firstPoint.lat]
          }
        }
      })
    }

    // 不再创建固定的起点标记，只使用动态的当前位置点
  }

  /**
   * 创建轨迹图层
   */
  private createTrackLayers(): void {
    this.clearExistingLayers()

    // 动态轨迹线图层
    this.map.addLayer({
      id: `${this.layerId}-line`,
      type: 'line',
      source: this.sourceId,
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': '#FF5722',  // 默认橙红色
        'line-width': 6,
        'line-opacity': 0.9
      }
    })

    // 当前位置点图层
    this.map.addLayer({
      id: `${this.layerId}-current`,
      type: 'circle',
      source: `${this.sourceId}-current`,
      paint: {
        'circle-radius': 8,
        'circle-color': '#00FF00',  // 绿色当前位置
        'circle-stroke-color': '#FFFFFF',
        'circle-stroke-width': 3
      }
    })

    // 不再创建固定的起点图层
  }

  /**
   * 更新轨迹进度 - 核心动态绘制逻辑
   */
  updateProgress(progress: number): void {
    this.currentProgress = Math.max(0, Math.min(1, progress))
    
    if (this.trackPoints.length === 0) return

    // 生成平滑的轨迹线坐标（参照原始实现）
    const coordinates = this.generateSmoothTrackCoordinates(this.currentProgress)

    // 更新动态轨迹线
    const source = this.map.getSource(this.sourceId) as mapboxgl.GeoJSONSource
    if (source) {
      source.setData({
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates
        }
      })
    }

    // 更新当前位置点 - 使用插值计算精确位置
    if (this.trackPoints.length > 1) {
      const interpolatedPosition = this.getInterpolatedPosition(this.currentProgress)
      const currentSource = this.map.getSource(`${this.sourceId}-current`) as mapboxgl.GeoJSONSource
      if (currentSource && interpolatedPosition) {
        currentSource.setData({
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'Point',
            coordinates: [interpolatedPosition.lng, interpolatedPosition.lat]
          }
        })

        // 根据插值位置的高度更新轨迹线颜色
        const currentElevation = interpolatedPosition.elevation || 0
        const color = this.getColorByElevation(currentElevation)

        if (this.map.getLayer(`${this.layerId}-line`)) {
          this.map.setPaintProperty(`${this.layerId}-line`, 'line-color', color)
        }
      }
    }

    // 只在关键进度点输出日志
    if (this.currentProgress === 0 || this.currentProgress === 1 || Math.random() < 0.005) {
      const coordinates = this.generateSmoothTrackCoordinates(this.currentProgress)
      console.log(`轨迹更新: 进度=${(this.currentProgress * 100).toFixed(1)}%, 显示点数=${coordinates.length}/${this.trackPoints.length}`)
    }
  }

  /**
   * 适配地图视图到轨迹范围
   */
  fitToTrack(): void {
    if (this.trackPoints.length === 0) return

    const bounds = new mapboxgl.LngLatBounds()
    this.trackPoints.forEach(point => {
      bounds.extend([point.lng, point.lat])
    })

    this.map.fitBounds(bounds, {
      padding: 50,
      duration: 1000,
      pitch: 60,
      bearing: 0
    })
  }

  /**
   * 清理已存在的数据源
   */
  private clearExistingSources(): void {
    const sourceIds = [
      this.sourceId,
      `${this.sourceId}-current`
    ]

    sourceIds.forEach(sourceId => {
      if (this.map.getSource(sourceId)) {
        this.map.removeSource(sourceId)
      }
    })
  }

  /**
   * 清理已存在的图层
   */
  private clearExistingLayers(): void {
    const layerIds = [
      `${this.layerId}-line`,
      `${this.layerId}-current`
    ]

    layerIds.forEach(layerId => {
      if (this.map.getLayer(layerId)) {
        this.map.removeLayer(layerId)
      }
    })
  }

  /**
   * 重置轨迹到起始状态
   */
  reset(): void {
    this.updateProgress(0)
  }

  /**
   * 销毁图层
   */
  destroy(): void {
    this.clearExistingLayers()
    this.clearExistingSources()
  }

  /**
   * 获取当前进度
   */
  getProgress(): number {
    return this.currentProgress
  }

  /**
   * 获取轨迹点总数
   */
  getPointCount(): number {
    return this.trackPoints.length
  }
}
