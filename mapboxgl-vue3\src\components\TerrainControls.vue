<template>
  <div class="terrain-controls">
    <div class="controls-header">
      <h3>地形控制</h3>
      <button 
        @click="toggleTerrain" 
        :class="['toggle-btn', { active: isTerrainEnabled }]"
        :disabled="!hasTerrainService"
      >
        {{ isTerrainEnabled ? '禁用地形' : '启用地形' }}
      </button>
    </div>

    <div v-if="isTerrainEnabled && currentTerrainInfo" class="terrain-details">
      <!-- 当前地形信息 -->
      <div class="info-section">
        <h4>当前地形信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">数据源:</span>
            <span class="value">{{ getSourceDisplayName(currentTerrainInfo.source) }}</span>
          </div>
          <div class="info-item">
            <span class="label">夸张度:</span>
            <span class="value">{{ currentTerrainInfo.exaggeration.toFixed(3) }}</span>
          </div>
          <div class="info-item">
            <span class="label">缩放级别:</span>
            <span class="value">{{ currentTerrainInfo.zoom.toFixed(1) }}</span>
          </div>
          <div class="info-item">
            <span class="label">中心坐标:</span>
            <span class="value">
              {{ currentTerrainInfo.center[1].toFixed(6) }}, {{ currentTerrainInfo.center[0].toFixed(6) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 手动控制 -->
      <div class="control-section">
        <h4>手动控制</h4>
        <div class="control-group">
          <label for="exaggeration-slider">地形夸张度: {{ manualExaggeration.toFixed(2) }}</label>
          <input
            id="exaggeration-slider"
            type="range"
            min="0.5"
            max="3.0"
            step="0.1"
            v-model.number="manualExaggeration"
            @input="onExaggerationChange"
            class="slider"
          />
        </div>
        
        <div class="control-group">
          <label for="terrain-source">地形数据源:</label>
          <select 
            id="terrain-source" 
            v-model="selectedSource" 
            @change="onSourceChange"
            class="select"
          >
            <option value="auto">自动选择</option>
            <option value="mapbox-dem">Mapbox DEM (全球)</option>
            <option value="mml-dem">MML DEM (芬兰)</option>
          </select>
        </div>

        <div class="control-buttons">
          <button @click="resetToAuto" class="btn-secondary">
            重置为自动
          </button>
          <button @click="updateTerrain" class="btn-primary">
            更新地形
          </button>
        </div>
      </div>

      <!-- 地形统计 -->
      <div class="stats-section">
        <h4>地形统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">初始化状态:</span>
            <span :class="['stat-value', { success: isInitialized }]">
              {{ isInitialized ? '已初始化' : '未初始化' }}
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">服务状态:</span>
            <span :class="['stat-value', { success: hasTerrainService }]">
              {{ hasTerrainService ? '正常' : '未启动' }}
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">错误状态:</span>
            <span :class="['stat-value', { error: !!error }]">
              {{ error || '无错误' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="!isTerrainEnabled" class="terrain-disabled">
      <p>地形功能已禁用</p>
      <p class="hint">启用地形以查看详细信息和控制选项</p>
    </div>

    <div v-else class="terrain-loading">
      <p>地形系统加载中...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useTerrain } from '@/composables/useTerrain'

// 使用地形管理器
const {
  isTerrainEnabled,
  isInitialized,
  currentTerrainInfo,
  error,
  hasTerrainService,
  toggleTerrain,
  updateTerrain,
  setTerrainExaggeration,
  resetToAutoMode
} = useTerrain()

// 手动控制状态
const manualExaggeration = ref(1.35)
const selectedSource = ref('auto')
const isManualMode = ref(false)

// 计算属性
const terrainStats = computed(() => ({
  isEnabled: isTerrainEnabled.value,
  isInitialized: isInitialized.value,
  hasService: hasTerrainService.value,
  error: error.value
}))

// 监听地形信息变化，同步手动控制值
watch(currentTerrainInfo, (newInfo) => {
  if (newInfo && !isManualMode.value) {
    manualExaggeration.value = newInfo.exaggeration
  }
}, { deep: true })

/**
 * 获取数据源显示名称
 */
function getSourceDisplayName(source: string): string {
  const sourceNames: Record<string, string> = {
    'mapbox-dem': 'Mapbox DEM (全球)',
    'mml-dem': 'MML DEM (芬兰)',
    '': '未知'
  }
  return sourceNames[source] || source
}

/**
 * 夸张度变化处理
 */
function onExaggerationChange(): void {
  isManualMode.value = true
  const source = selectedSource.value === 'auto' ? undefined : selectedSource.value
  setTerrainExaggeration(manualExaggeration.value, source)
}

/**
 * 数据源变化处理
 */
function onSourceChange(): void {
  isManualMode.value = true
  const source = selectedSource.value === 'auto' ? undefined : selectedSource.value
  setTerrainExaggeration(manualExaggeration.value, source)
}

/**
 * 重置为自动模式
 */
function resetToAuto(): void {
  isManualMode.value = false
  selectedSource.value = 'auto'
  resetToAutoMode()
}
</script>

<style scoped>
.terrain-controls {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 400px;
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
}

.controls-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.toggle-btn {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
  color: #495057;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.toggle-btn:hover:not(:disabled) {
  background: #f8f9fa;
}

.toggle-btn.active {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.toggle-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.terrain-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-section, .control-section, .stats-section {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
}

.info-section h4, .control-section h4, .stats-section h4 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 14px;
}

.info-grid, .stats-grid {
  display: grid;
  gap: 8px;
}

.info-item, .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label, .stat-label {
  font-weight: 500;
  color: #6c757d;
  font-size: 12px;
}

.value, .stat-value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 12px;
  font-family: 'Courier New', monospace;
}

.stat-value.success {
  color: #28a745;
}

.stat-value.error {
  color: #dc3545;
}

.control-group {
  margin-bottom: 12px;
}

.control-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #495057;
  font-size: 12px;
}

.slider {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: #dee2e6;
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #007bff;
  cursor: pointer;
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #007bff;
  cursor: pointer;
  border: none;
}

.select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  font-size: 12px;
}

.control-buttons {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.btn-primary, .btn-secondary {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.btn-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.btn-secondary {
  background: white;
  color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  background: #f8f9fa;
}

.terrain-disabled, .terrain-loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.hint {
  font-size: 12px;
  color: #adb5bd;
  margin-top: 8px;
}
</style>
