/**
 * 通用类型定义
 */

// 基础几何类型
export interface LngLat {
  lng: number
  lat: number
}

export interface Point {
  x: number
  y: number
}

export interface Bounds {
  north: number
  south: number
  east: number
  west: number
}

// 测量系统
export enum MeasurementSystem {
  METRIC = 'METRIC',
  IMPERIAL = 'IMPERIAL'
}

// 语言代码
export type LanguageCode = 
  | 'zh-CN' | 'en-US' | 'ja-JP' | 'ko-KR' | 'fr-FR' 
  | 'de-DE' | 'es-ES' | 'it-IT' | 'pt-PT' | 'ru-RU'
  | 'ar-SA' | 'hi-IN' | 'th-TH' | 'vi-VN' | 'id-ID'
  | 'ms-MY' | 'tl-PH' | 'tr-TR' | 'pl-PL' | 'nl-NL'
  | 'sv-SE' | 'da-DK' | 'no-NO' | 'fi-FI' | 'cs-CZ'

// 地图样式
export enum MapStyle {
  STREETS = 'streets',
  SATELLITE = 'satellite',
  OUTDOORS = 'outdoors',
  DARK = 'dark',
  LIGHT = 'light',
  TERRAIN = 'terrain'
}

// 应用主题
export enum Theme {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto'
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: AppError
  timestamp: number
}

// 用户设置
export interface UserSettings {
  language: LanguageCode
  measurementSystem: MeasurementSystem
  theme: Theme
  mapStyle: MapStyle
  autoDetectLocation: boolean
  enableAnalytics: boolean
}

// 应用配置
export interface AppConfig {
  mapboxToken: string
  apiBaseUrl: string
  version: string
  buildTime: string
  features: {
    analytics: boolean
    debugging: boolean
    experimental: boolean
  }
}

// 性能指标
export interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  renderTime: number
  loadTime: number
}

// 事件类型
export interface AppEvent {
  type: string
  timestamp: number
  data?: any
}

// 工具函数类型
export type Nullable<T> = T | null
export type Optional<T> = T | undefined
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}
