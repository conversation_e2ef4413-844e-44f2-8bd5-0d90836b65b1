<template>
  <div class="map-view">
    <!-- 地图容器 -->
    <div ref="mapContainer" class="map-container" />
    
    <!-- 控制面板 -->
    <div class="control-panel" :class="{ collapsed: !showControls }">
      <div class="panel-header">
        <h3>{{ $t('map.title') }}</h3>
        <button @click="toggleControls" class="toggle-btn">
          {{ showControls ? '−' : '+' }}
        </button>
      </div>
      
      <div v-show="showControls" class="panel-content">
        <!-- 地图样式选择 -->
        <div class="control-group">
          <label>{{ $t('map.styles.title') }}</label>
          <select v-model="currentStyle" @change="handleStyleChange">
            <option value="streets">{{ $t('map.styles.streets') }}</option>
            <option value="satellite">{{ $t('map.styles.satellite') }}</option>
            <option value="outdoors">{{ $t('map.styles.outdoors') }}</option>
            <option value="dark">{{ $t('map.styles.dark') }}</option>
            <option value="light">{{ $t('map.styles.light') }}</option>
            <option value="terrain">{{ $t('map.styles.terrain') }}</option>
          </select>
        </div>

        <!-- 3D地形控制 -->
        <div class="control-group">
          <label>
            <input 
              type="checkbox" 
              v-model="is3DEnabled" 
              @change="handle3DToggle"
            />
            {{ $t('map.terrain.enable') }}
          </label>
          
          <div v-if="is3DEnabled" class="terrain-controls">
            <label>{{ $t('map.terrain.exaggeration') }}</label>
            <input 
              type="range" 
              min="0.5" 
              max="3.0" 
              step="0.1"
              v-model="terrainExaggeration"
              @input="handleTerrainExaggerationChange"
            />
            <span>{{ terrainExaggeration }}</span>
          </div>
        </div>

        <!-- 相机控制 -->
        <div class="control-group">
          <label>{{ $t('map.camera.pitch') }}</label>
          <input 
            type="range" 
            min="0" 
            max="85" 
            v-model="pitch"
            @input="handlePitchChange"
          />
          <span>{{ pitch }}°</span>
        </div>

        <div class="control-group">
          <label>{{ $t('map.camera.bearing') }}</label>
          <input 
            type="range" 
            min="0" 
            max="360" 
            v-model="bearing"
            @input="handleBearingChange"
          />
          <span>{{ bearing }}°</span>
        </div>

        <!-- 快速导航 -->
        <div class="control-group">
          <label>{{ $t('common.navigation') }}</label>
          <div class="button-group">
            <button @click="flyToBeijing">北京</button>
            <button @click="flyToShanghai">上海</button>
            <button @click="flyToShenzhen">深圳</button>
          </div>
        </div>

        <!-- 动画控制 -->
        <div class="control-group">
          <label>{{ $t('map.animation.title') }}</label>
          <div class="button-group">
            <button @click="startRotation" :disabled="isAnimating">
              {{ $t('map.animation.start') }}
            </button>
            <button @click="stopRotation" :disabled="!isAnimating">
              {{ $t('map.animation.stop') }}
            </button>
          </div>
        </div>

        <!-- 重置按钮 -->
        <div class="control-group">
          <button @click="resetView" class="reset-btn">
            {{ $t('map.camera.reset') }}
          </button>
        </div>
      </div>
    </div>

    <!-- 状态信息 -->
    <div class="status-info">
      <div class="coordinates">
        <span>{{ $t('map.camera.center') }}: {{ formatCoordinates(center) }}</span>
        <span>{{ $t('map.camera.zoom') }}: {{ zoom.toFixed(2) }}</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="!isMapLoaded" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p>{{ $t('common.loading') }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import { useMapStore } from '@/stores/mapStore'
import { useSettingsStore } from '@/stores/settingsStore'
import { useMap } from '@/composables/useMap'
import type { MapStyle } from '@/types/common'

const { t } = useI18n()
const mapStore = useMapStore()
const settingsStore = useSettingsStore()

// 响应式引用
const mapContainer = ref<HTMLElement | null>(null)
const showControls = ref(true)

// 从store获取状态
const {
  currentStyle,
  center,
  zoom,
  pitch,
  bearing,
  is3DEnabled,
  terrainExaggeration,
  isAnimating,
  isLoaded: isMapLoaded
} = storeToRefs(mapStore)

// 使用地图composable
const { mapEngine, initializeMap, isInitialized } = useMap(mapContainer)

// 动画相关
let rotationAnimation: number | null = null

// 生命周期
onMounted(async () => {
  try {
    await initializeMap({
      style: getMapboxStyle(currentStyle.value),
      center: [center.value.lng, center.value.lat],
      zoom: zoom.value,
      pitch: pitch.value,
      bearing: bearing.value,
      accessToken: import.meta.env.VITE_MAPBOX_TOKEN || 'your-mapbox-token'
    })
    
    console.log('✅ 地图视图初始化完成')
  } catch (error) {
    console.error('❌ 地图视图初始化失败:', error)
  }
})

onUnmounted(() => {
  if (rotationAnimation) {
    cancelAnimationFrame(rotationAnimation)
  }
})

// 监听器
watch(currentStyle, (newStyle) => {
  if (mapEngine.value) {
    mapEngine.value.setStyle(getMapboxStyle(newStyle))
  }
})

// 事件处理
const toggleControls = () => {
  showControls.value = !showControls.value
}

const handleStyleChange = () => {
  mapStore.setStyle(currentStyle.value)
}

const handle3DToggle = () => {
  if (is3DEnabled.value) {
    mapStore.enable3D()
    enableTerrain()
  } else {
    mapStore.disable3D()
    disableTerrain()
  }
}

const handleTerrainExaggerationChange = () => {
  mapStore.setTerrainExaggeration(terrainExaggeration.value)
  if (mapEngine.value && is3DEnabled.value) {
    mapEngine.value.enableTerrain({
      source: 'mapbox-dem',
      exaggeration: terrainExaggeration.value
    })
  }
}

const handlePitchChange = () => {
  mapStore.setPitch(pitch.value)
  if (mapEngine.value) {
    mapEngine.value.map?.setPitch(pitch.value)
  }
}

const handleBearingChange = () => {
  mapStore.setBearing(bearing.value)
  if (mapEngine.value) {
    mapEngine.value.map?.setBearing(bearing.value)
  }
}

// 地形功能
const enableTerrain = () => {
  if (!mapEngine.value) return
  
  // 添加DEM数据源
  mapEngine.value.addSource('mapbox-dem', {
    type: 'raster-dem',
    url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
    tileSize: 512,
    maxzoom: 14
  })
  
  // 启用地形
  mapEngine.value.enableTerrain({
    source: 'mapbox-dem',
    exaggeration: terrainExaggeration.value
  })
  
  // 添加天空
  mapEngine.value.setSky({
    'sky-type': 'atmosphere',
    'sky-atmosphere-sun': [0.0, 0.0],
    'sky-atmosphere-sun-intensity': 15
  })
}

const disableTerrain = () => {
  if (!mapEngine.value) return
  
  mapEngine.value.disableTerrain()
  mapEngine.value.removeSource('mapbox-dem')
}

// 导航功能
const flyToBeijing = () => {
  mapStore.flyToBeijing()
  if (mapEngine.value) {
    mapEngine.value.flyTo([116.4074, 39.9042], 12)
  }
}

const flyToShanghai = () => {
  mapStore.flyToShanghai()
  if (mapEngine.value) {
    mapEngine.value.flyTo([121.4737, 31.2304], 12)
  }
}

const flyToShenzhen = () => {
  mapStore.flyToShenzhen()
  if (mapEngine.value) {
    mapEngine.value.flyTo([114.0579, 22.5431], 12)
  }
}

// 动画功能
const startRotation = () => {
  if (!mapEngine.value || isAnimating.value) return
  
  mapStore.startAnimation()
  
  const rotate = () => {
    if (!mapEngine.value || !isAnimating.value) return
    
    const currentBearing = mapEngine.value.map?.getBearing() || 0
    const newBearing = (currentBearing + 0.5) % 360
    
    mapEngine.value.map?.setBearing(newBearing)
    mapStore.setBearing(newBearing)
    
    rotationAnimation = requestAnimationFrame(rotate)
  }
  
  rotate()
}

const stopRotation = () => {
  mapStore.stopAnimation()
  if (rotationAnimation) {
    cancelAnimationFrame(rotationAnimation)
    rotationAnimation = null
  }
}

const resetView = () => {
  mapStore.resetView()
  if (mapEngine.value) {
    mapEngine.value.flyTo([116.4074, 39.9042], 10, {
      pitch: 0,
      bearing: 0
    })
  }
}

// 工具函数
const getMapboxStyle = (style: MapStyle): string => {
  const styleMap: Record<MapStyle, string> = {
    streets: 'mapbox://styles/mapbox/streets-v12',
    satellite: 'mapbox://styles/mapbox/satellite-streets-v12',
    outdoors: 'mapbox://styles/mapbox/outdoors-v12',
    dark: 'mapbox://styles/mapbox/dark-v11',
    light: 'mapbox://styles/mapbox/light-v11',
    terrain: 'mapbox://styles/mapbox/outdoors-v12'
  }
  return styleMap[style] || styleMap.streets
}

const formatCoordinates = (coords: { lng: number; lat: number }): string => {
  return `${coords.lng.toFixed(4)}, ${coords.lat.toFixed(4)}`
}
</script>

<style scoped>
.map-view {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.map-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 280px;
  max-width: 320px;
  transition: all 0.3s ease;
  z-index: 1000;
}

.control-panel.collapsed {
  min-width: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.toggle-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
}

.toggle-btn:hover {
  background: #f0f0f0;
}

.panel-content {
  padding: 16px;
  max-height: 70vh;
  overflow-y: auto;
}

.control-group {
  margin-bottom: 20px;
}

.control-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 14px;
}

.control-group select,
.control-group input[type="range"] {
  width: 100%;
  margin-bottom: 4px;
}

.control-group input[type="range"] {
  margin-right: 8px;
}

.terrain-controls {
  margin-top: 12px;
  padding-left: 20px;
}

.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.button-group button {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.button-group button:hover {
  background: #f0f0f0;
}

.button-group button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reset-btn {
  width: 100%;
  padding: 12px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.reset-btn:hover {
  background: #ff3742;
}

.status-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 6px;
  font-family: monospace;
  font-size: 12px;
  z-index: 1000;
}

.coordinates span {
  display: block;
  margin-bottom: 4px;
}

.coordinates span:last-child {
  margin-bottom: 0;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
