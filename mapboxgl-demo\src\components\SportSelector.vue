<template>
  <div class="sport-selector">
    <n-card size="small" class="selector-card">
      <n-space vertical size="small">
        <div class="selector-title">
          {{ $t('sports.title') }}
        </div>
        
        <n-radio-group 
          v-model:value="localSelected" 
          @update:value="handleSelect"
          size="small"
        >
          <n-space vertical size="small">
            <n-radio
              v-for="sport in availableSports"
              :key="sport"
              :value="sport"
              :label="$t(`sports.${sport}`)"
            >
              <template #default>
                <n-space align="center" size="small">
                  <n-icon size="16" :color="getSportColor(sport)">
                    <component :is="getSportIcon(sport)" />
                  </n-icon>
                  <span>{{ $t(`sports.${sport}`) }}</span>
                </n-space>
              </template>
            </n-radio>
          </n-space>
        </n-radio-group>
      </n-space>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { NCard, NSpace, NRadioGroup, NRadio, NIcon } from 'naive-ui'
import {
  Walk as RunningIcon,
  Bicycle as CyclingIcon,
  Walk as HikingIcon,
  Snow as SkiingIcon,
  Water as SwimmingIcon
} from '@vicons/ionicons5'
import type { SportType } from '@/types'

// Props
interface Props {
  selected: SportType
  options?: SportType[]
}

const props = withDefaults(defineProps<Props>(), {
  options: () => ['running', 'cycling', 'hiking', 'skiing', 'swimming']
})

// Emits
const emit = defineEmits<{
  select: [sport: SportType]
}>()

// 本地状态
const localSelected = ref(props.selected)

// 可用的运动类型
const availableSports = computed(() => props.options)

// 获取运动图标
const getSportIcon = (sport: SportType) => {
  const iconMap = {
    running: RunningIcon,
    cycling: CyclingIcon,
    hiking: HikingIcon,
    skiing: SkiingIcon,
    swimming: SwimmingIcon
  }
  return iconMap[sport] || RunningIcon
}

// 获取运动颜色
const getSportColor = (sport: SportType): string => {
  const colorMap = {
    running: '#ff6b6b',
    cycling: '#4ecdc4',
    hiking: '#45b7d1',
    skiing: '#96ceb4',
    swimming: '#feca57'
  }
  return colorMap[sport] || '#666'
}

// 处理选择
const handleSelect = (sport: SportType) => {
  emit('select', sport)
}

// 监听props变化
watch(
  () => props.selected,
  (newSelected) => {
    localSelected.value = newSelected
  }
)
</script>

<style scoped>
.sport-selector {
  user-select: none;
}

.selector-card {
  min-width: 160px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
}

.selector-card :deep(.n-card__content) {
  padding: 12px;
}

.selector-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--n-text-color-1);
  text-align: center;
}

/* 深色主题适配 */
.dark .selector-card {
  background: rgba(0, 0, 0, 0.8);
}
</style>
