import { ref, onMounted, onUnmounted } from 'vue'
import { usePositionStore } from '@/stores/position'

/**
 * 动画循环系统
 * 参照 maps.suunto.com 的 requestAnimationFrame 驱动的 60FPS 动画
 */
export const useAnimationLoop = () => {
  const positionStore = usePositionStore()
  const animationId = ref<number>()
  const isRunning = ref(false)

  /**
   * 动画循环函数
   * 参照原项目的 usePosition hook 中的动画逻辑
   */
  const animate = () => {
    // 更新播放进度
    positionStore.updatePosition()
    
    // 继续动画循环
    if (isRunning.value) {
      animationId.value = requestAnimationFrame(animate)
    }
  }

  /**
   * 启动动画循环
   */
  const start = () => {
    if (!isRunning.value) {
      isRunning.value = true
      animationId.value = requestAnimationFrame(animate)
      console.log('动画循环已启动')
    }
  }

  /**
   * 停止动画循环
   */
  const stop = () => {
    if (isRunning.value && animationId.value) {
      cancelAnimationFrame(animationId.value)
      isRunning.value = false
      animationId.value = undefined
      console.log('动画循环已停止')
    }
  }

  /**
   * 重启动画循环
   */
  const restart = () => {
    stop()
    start()
  }

  // 生命周期管理
  onMounted(() => {
    start()
  })

  onUnmounted(() => {
    stop()
  })

  return {
    isRunning,
    start,
    stop,
    restart
  }
}
