import { ref, computed, shallowRef, watch } from 'vue'
import type { Track, CameraPosition, LatLng } from '@/types'
import { calculateCenter, calculateBounds, calculateBearing } from '@/utils/geo'
import { lerp, smoothstep } from '@/utils/math'

// 3D相机路径系统（复用原项目算法）
export const useCameraPath = (track?: Track) => {
  const cameraPath = shallowRef<CameraPosition[]>([])
  const isInitialized = ref(false)

  // 生成相机路径
  const generateCameraPath = (trackData: Track) => {
    if (!trackData || trackData.points.length < 2) {
      cameraPath.value = []
      return
    }

    const points = trackData.points
    const path: CameraPosition[] = []
    
    // 计算轨迹边界和中心
    const bounds = calculateBounds(points)
    const center = calculateCenter(points)
    
    // 计算合适的缩放级别
    const latDiff = bounds.north - bounds.south
    const lngDiff = bounds.east - bounds.west
    const maxDiff = Math.max(latDiff, lngDiff)
    const baseZoom = Math.max(10, 18 - Math.log2(maxDiff * 100))

    // 为每个轨迹点生成相机位置
    for (let i = 0; i < points.length; i++) {
      const point = points[i]
      const nextPoint = points[i + 1]
      
      // 计算相机中心（稍微偏移以获得更好的视角）
      const cameraCenter: LatLng = {
        lat: point.lat,
        lng: point.lng
      }

      // 计算方位角
      let bearing = 0
      if (nextPoint) {
        bearing = calculateBearing(point, nextPoint)
      } else if (i > 0) {
        bearing = calculateBearing(points[i - 1], point)
      }

      // 计算俯仰角（基于海拔变化）
      let pitch = 45 // 默认俯仰角
      if (nextPoint && point.elevation !== undefined && nextPoint.elevation !== undefined) {
        const elevationDiff = nextPoint.elevation - point.elevation
        const distance = Math.sqrt(
          Math.pow(nextPoint.lat - point.lat, 2) + 
          Math.pow(nextPoint.lng - point.lng, 2)
        )
        const slope = elevationDiff / (distance * 111000) // 粗略转换为米
        pitch = Math.max(20, Math.min(70, 45 + slope * 10))
      }

      // 动态缩放（在转弯处放大，直线处缩小）
      let zoom = baseZoom
      if (i > 0 && i < points.length - 1) {
        const prevBearing = calculateBearing(points[i - 1], point)
        const nextBearing = calculateBearing(point, points[i + 1])
        const bearingDiff = Math.abs(nextBearing - prevBearing)
        const normalizedDiff = Math.min(bearingDiff, 360 - bearingDiff)
        
        // 在急转弯处放大
        if (normalizedDiff > 30) {
          zoom += 1
        }
      }

      path.push({
        center: cameraCenter,
        zoom: Math.max(10, Math.min(18, zoom)),
        bearing,
        pitch
      })
    }

    cameraPath.value = path
    isInitialized.value = true
  }

  // 根据播放进度获取相机位置
  const getCameraPosition = (progress: number): CameraPosition | null => {
    if (!cameraPath.value.length) return null

    const clampedProgress = Math.max(0, Math.min(1, progress))
    const totalPoints = cameraPath.value.length
    
    if (totalPoints === 1) {
      return cameraPath.value[0]
    }

    // 计算当前位置在路径中的索引
    const exactIndex = clampedProgress * (totalPoints - 1)
    const lowerIndex = Math.floor(exactIndex)
    const upperIndex = Math.min(lowerIndex + 1, totalPoints - 1)
    const t = exactIndex - lowerIndex

    const lowerPos = cameraPath.value[lowerIndex]
    const upperPos = cameraPath.value[upperIndex]

    // 使用平滑插值
    const smoothT = smoothstep(0, 1, t)

    return {
      center: {
        lat: lerp(lowerPos.center.lat, upperPos.center.lat, smoothT),
        lng: lerp(lowerPos.center.lng, upperPos.center.lng, smoothT)
      },
      zoom: lerp(lowerPos.zoom, upperPos.zoom, smoothT),
      bearing: lerpAngle(lowerPos.bearing, upperPos.bearing, smoothT),
      pitch: lerp(lowerPos.pitch, upperPos.pitch, smoothT)
    }
  }

  // 角度插值（处理360度边界）
  const lerpAngle = (a: number, b: number, t: number): number => {
    const diff = ((b - a + 540) % 360) - 180
    return (a + diff * t + 360) % 360
  }

  // 获取轨迹边界
  const getBounds = computed(() => {
    if (!track?.points.length) return null
    return calculateBounds(track.points)
  })

  // 获取轨迹中心
  const getCenter = computed(() => {
    if (!track?.points.length) return null
    return calculateCenter(track.points)
  })

  // 监听轨迹变化
  watch(
    () => track,
    (newTrack) => {
      if (newTrack) {
        generateCameraPath(newTrack)
      } else {
        cameraPath.value = []
        isInitialized.value = false
      }
    },
    { immediate: true }
  )

  return {
    cameraPath: computed(() => cameraPath.value),
    isInitialized: computed(() => isInitialized.value),
    getCameraPosition,
    getBounds,
    getCenter,
    generateCameraPath
  }
}
