/**
 * 地形管理组合式 API
 * 提供响应式的地形控制功能
 */

import { ref, computed, onUnmounted } from 'vue'
import type mapboxgl from 'mapbox-gl'
import { TerrainService } from '@/services/TerrainService'

export interface TerrainInfo {
  source: string
  exaggeration: number
  center: [number, number]
  zoom: number
}

export function useTerrain(varianceImageUrl?: string) {
  // 响应式状态
  const terrainService = ref<TerrainService | null>(null)
  const isTerrainEnabled = ref(false)
  const isInitialized = ref(false)
  const currentTerrainInfo = ref<TerrainInfo | null>(null)
  const error = ref<string | null>(null)

  // 计算属性
  const hasTerrainService = computed(() => terrainService.value !== null)
  const terrainSource = computed(() => currentTerrainInfo.value?.source || '')
  const terrainExaggeration = computed(() => currentTerrainInfo.value?.exaggeration || 1)

  /**
   * 初始化地形服务
   */
  async function initializeTerrain(map: mapboxgl.Map): Promise<void> {
    try {
      error.value = null
      
      // 创建地形服务实例
      terrainService.value = new TerrainService(map, varianceImageUrl)
      
      // 初始化地形数据源
      await terrainService.value.initializeTerrainSources()
      
      // 启用自动更新
      terrainService.value.enableAutoUpdate()
      
      // 初始更新地形
      terrainService.value.updateTerrain()
      
      isInitialized.value = true
      isTerrainEnabled.value = true
      
      // 更新地形信息
      updateTerrainInfo()
      
      console.log('地形服务初始化完成')
    } catch (err) {
      error.value = err instanceof Error ? err.message : '地形初始化失败'
      console.error('地形服务初始化失败:', err)
      throw err
    }
  }

  /**
   * 更新地形信息
   */
  function updateTerrainInfo(): void {
    if (terrainService.value) {
      currentTerrainInfo.value = terrainService.value.getCurrentTerrainInfo()
    }
  }

  /**
   * 手动更新地形
   */
  function updateTerrain(): void {
    if (terrainService.value) {
      terrainService.value.updateTerrain()
      updateTerrainInfo()
    }
  }

  /**
   * 启用地形
   */
  function enableTerrain(): void {
    if (terrainService.value) {
      terrainService.value.updateTerrain()
      isTerrainEnabled.value = true
      updateTerrainInfo()
      console.log('地形已启用')
    }
  }

  /**
   * 禁用地形
   */
  function disableTerrain(): void {
    if (terrainService.value) {
      terrainService.value.disableTerrain()
      isTerrainEnabled.value = false
      currentTerrainInfo.value = null
      console.log('地形已禁用')
    }
  }

  /**
   * 切换地形状态
   */
  function toggleTerrain(): void {
    if (isTerrainEnabled.value) {
      disableTerrain()
    } else {
      enableTerrain()
    }
  }

  /**
   * 获取地形统计信息
   */
  function getTerrainStats(): {
    isEnabled: boolean
    isInitialized: boolean
    hasService: boolean
    currentInfo: TerrainInfo | null
    error: string | null
  } {
    return {
      isEnabled: isTerrainEnabled.value,
      isInitialized: isInitialized.value,
      hasService: hasTerrainService.value,
      currentInfo: currentTerrainInfo.value,
      error: error.value
    }
  }

  /**
   * 监听地形变化
   */
  function watchTerrainChanges(callback: (info: TerrainInfo) => void): () => void {
    let intervalId: number | undefined

    if (terrainService.value) {
      intervalId = window.setInterval(() => {
        updateTerrainInfo()
        if (currentTerrainInfo.value) {
          callback(currentTerrainInfo.value)
        }
      }, 1000) // 每秒更新一次
    }

    // 返回清理函数
    return () => {
      if (intervalId) {
        window.clearInterval(intervalId)
      }
    }
  }

  /**
   * 设置地形夸张度（手动覆盖）
   */
  function setTerrainExaggeration(exaggeration: number, source?: string): void {
    if (terrainService.value && terrainService.value['map']) {
      const terrainSource = source || terrainSource.value || 'mapbox-dem'
      
      terrainService.value['map'].setTerrain({
        source: terrainSource,
        exaggeration
      })
      
      // 更新当前信息
      if (currentTerrainInfo.value) {
        currentTerrainInfo.value.exaggeration = exaggeration
        if (source) {
          currentTerrainInfo.value.source = source
        }
      }
      
      console.log('手动设置地形夸张度:', { exaggeration, source: terrainSource })
    }
  }

  /**
   * 重置地形为自动模式
   */
  function resetToAutoMode(): void {
    if (terrainService.value) {
      terrainService.value.updateTerrain()
      updateTerrainInfo()
      console.log('地形已重置为自动模式')
    }
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    if (terrainService.value) {
      terrainService.value.destroy()
      terrainService.value = null
    }
  })

  return {
    // 状态
    isTerrainEnabled,
    isInitialized,
    currentTerrainInfo,
    error,
    
    // 计算属性
    hasTerrainService,
    terrainSource,
    terrainExaggeration,
    
    // 方法
    initializeTerrain,
    updateTerrain,
    enableTerrain,
    disableTerrain,
    toggleTerrain,
    getTerrainStats,
    watchTerrainChanges,
    setTerrainExaggeration,
    resetToAutoMode,
    updateTerrainInfo
  }
}
