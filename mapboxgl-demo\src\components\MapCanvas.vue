<template>
  <div class="map-container" ref="mapContainer">
    <!-- 地图容器 -->
    <div id="map" class="map-canvas"></div>
    
    <!-- 控制面板 -->
    <div class="controls-panel" v-if="showControls">
      <CameraControls 
        :position="positionStore.position"
        :is-playing="positionStore.isPlaying"
        :speed="positionStore.speed"
        @position-change="positionStore.setPosition"
        @toggle-play="positionStore.togglePlay"
        @speed-change="positionStore.setSpeed"
      />
    </div>

    <!-- 运动类型选择器 -->
    <div class="sport-selector" v-if="showSportSelector">
      <SportSelector 
        :selected="selectedSport"
        @select="handleSportSelect"
      />
    </div>

    <!-- 加载状态 -->
    <div class="loading-overlay" v-if="isLoading">
      <n-spin size="large">
        <template #description>{{ $t('map.loading') }}</template>
      </n-spin>
    </div>

    <!-- 错误状态 -->
    <div class="error-overlay" v-if="error">
      <n-alert type="error" :title="$t('map.error')">
        {{ error }}
      </n-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { NSpin, NAlert } from 'naive-ui'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'

import { usePositionStore } from '@/stores/position'
import { useThemeStore } from '@/stores/theme'
import { useCameraPath } from '@/composables/useCameraPath'
import CameraControls from './CameraControls.vue'
import SportSelector from './SportSelector.vue'

import type { Track, SportType, MapStyle } from '@/types'

// Props
interface Props {
  track?: Track
  style?: MapStyle
  showControls?: boolean
  showSportSelector?: boolean
  autoPlay?: boolean
  loop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  style: 'outdoors',
  showControls: true,
  showSportSelector: false,
  autoPlay: false,
  loop: false
})

// Emits
const emit = defineEmits<{
  trackLoaded: [track: Track]
  sportChange: [sport: SportType]
  error: [error: string]
}>()

// 状态
const mapContainer = ref<HTMLDivElement>()
const map = ref<mapboxgl.Map>()
const isLoading = ref(true)
const error = ref<string>()
const selectedSport = ref<SportType>('running')

// Stores
const positionStore = usePositionStore()
const themeStore = useThemeStore()

// 组合式函数
const { getCameraPosition, getBounds, getCenter } = useCameraPath(props.track)

// Mapbox访问令牌（从环境变量获取）
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_TOKEN

// 初始化地图
const initializeMap = async () => {
  if (!mapContainer.value) return

  try {
    // 检查 token 是否存在
    if (!MAPBOX_TOKEN) {
      throw new Error('Mapbox access token is required. Please set VITE_MAPBOX_TOKEN in your .env file.')
    }

    mapboxgl.accessToken = MAPBOX_TOKEN

    // 创建地图实例
    map.value = new mapboxgl.Map({
      container: 'map',
      style: `mapbox://styles/mapbox/${props.style}-v11`,
      center: getCenter.value || [0, 0],
      zoom: 10,
      pitch: 45,
      bearing: 0,
      antialias: true
    })

    // 地图加载完成
    map.value.on('load', () => {
      isLoading.value = false
      if (props.track) {
        loadTrack(props.track)
      }
    })

    // 地图错误处理
    map.value.on('error', (e) => {
      error.value = e.error?.message || 'Map loading failed'
      isLoading.value = false
      emit('error', error.value)
    })

  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Unknown error'
    isLoading.value = false
    emit('error', error.value)
  }
}

// 加载轨迹数据
const loadTrack = (track: Track) => {
  if (!map.value || !track.points.length) return

  try {
    // 创建轨迹线的GeoJSON数据
    const lineData = {
      type: 'Feature' as const,
      properties: {},
      geometry: {
        type: 'LineString' as const,
        coordinates: track.points.map(point => [point.lng, point.lat, point.elevation || 0])
      }
    }

    // 添加数据源
    if (map.value.getSource('track-line')) {
      (map.value.getSource('track-line') as mapboxgl.GeoJSONSource).setData(lineData)
    } else {
      map.value.addSource('track-line', {
        type: 'geojson',
        data: lineData,
        lineMetrics: true
      })
    }

    // 添加轨迹线图层
    if (!map.value.getLayer('track-line-layer')) {
      map.value.addLayer({
        id: 'track-line-layer',
        type: 'line',
        source: 'track-line',
        layout: {
          'line-join': 'round',
          'line-cap': 'round'
        },
        paint: {
          'line-color': '#ff6b6b',
          'line-width': 4,
          'line-opacity': 0.8
        }
      })
    }

    // 适配地图视图到轨迹
    const bounds = getBounds.value
    if (bounds) {
      map.value.fitBounds([
        [bounds.west, bounds.south],
        [bounds.east, bounds.north]
      ], {
        padding: 50,
        duration: 1000
      })
    }

    emit('trackLoaded', track)

    // 自动播放
    if (props.autoPlay) {
      nextTick(() => {
        positionStore.play()
      })
    }

  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load track'
    emit('error', error.value)
  }
}

// 更新相机位置
const updateCamera = (position: number) => {
  if (!map.value) return

  const cameraPos = getCameraPosition(position)
  if (!cameraPos) return

  map.value.easeTo({
    center: [cameraPos.center.lng, cameraPos.center.lat],
    zoom: cameraPos.zoom,
    bearing: cameraPos.bearing,
    pitch: cameraPos.pitch,
    duration: 100
  })
}

// 处理运动类型选择
const handleSportSelect = (sport: SportType) => {
  selectedSport.value = sport
  emit('sportChange', sport)
}

// 监听播放位置变化
watch(
  () => positionStore.position,
  (newPosition) => {
    updateCamera(newPosition)
  }
)

// 监听轨迹变化
watch(
  () => props.track,
  (newTrack) => {
    if (newTrack && map.value) {
      loadTrack(newTrack)
    }
  }
)

// 监听主题变化
watch(
  () => themeStore.currentTheme,
  (newTheme) => {
    if (map.value) {
      const style = newTheme === 'dark' ? 'dark' : props.style
      map.value.setStyle(`mapbox://styles/mapbox/${style}-v12`)
    }
  }
)

// 生命周期
onMounted(() => {
  initializeMap()
})

onUnmounted(() => {
  if (map.value) {
    map.value.remove()
  }
})
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.map-canvas {
  width: 100%;
  height: 100%;
}

.controls-panel {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.sport-selector {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 2000;
}

.error-overlay {
  background: rgba(255, 255, 255, 0.95);
}
</style>
