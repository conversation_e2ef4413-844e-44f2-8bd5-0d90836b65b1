import type { RouteRecordRaw } from 'vue-router'

// 路由配置（等同原项目的 Routes.ts）
export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/TrackDemo.vue'),
    meta: {
      title: '轨迹演示'
    }
  },
  {
    path: '/workout/:id',
    name: 'WorkoutDetail',
    component: () => import('@/views/TrackDemo.vue'),
    props: true,
    meta: {
      title: '运动轨迹详情'
    }
  },
  {
    path: '/demo',
    name: 'Demo',
    component: () => import('@/views/TrackDemo.vue'),
    meta: {
      title: '演示模式'
    }
  }
]
