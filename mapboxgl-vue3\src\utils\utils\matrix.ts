/**
 * 矩阵运算工具
 * 参照 maps.suunto.com 的矩阵运算实现
 */

/**
 * 构建 4x4 矩阵
 */
export function buildMatrix(
  a: number[],
  b: number[],
  c: number[],
  d: number[]
): number[] {
  return a.concat(b, c, d)
}

/**
 * 4x4 矩阵乘以 4D 向量
 */
export function multiplyMat4Vec4(
  m: number[],
  [x, y, z, w]: number[]
): number[] {
  return [
    x * m[0] + y * m[4] + z * m[8] + w * m[12],
    x * m[1] + y * m[5] + z * m[9] + w * m[13],
    x * m[2] + y * m[6] + z * m[10] + w * m[14],
    x * m[3] + y * m[7] + z * m[11] + w * m[15],
  ]
}

/**
 * 4x4 矩阵乘法
 */
export function multiplyMat4Mat4(a: number[], b: number[]): number[] {
  const result = new Array(16)
  
  for (let i = 0; i < 4; i++) {
    for (let j = 0; j < 4; j++) {
      result[i * 4 + j] = 
        a[i * 4 + 0] * b[0 * 4 + j] +
        a[i * 4 + 1] * b[1 * 4 + j] +
        a[i * 4 + 2] * b[2 * 4 + j] +
        a[i * 4 + 3] * b[3 * 4 + j]
    }
  }
  
  return result
}

/**
 * 创建单位矩阵
 */
export function identityMatrix(): number[] {
  return [
    1, 0, 0, 0,
    0, 1, 0, 0,
    0, 0, 1, 0,
    0, 0, 0, 1
  ]
}

/**
 * 创建平移矩阵
 */
export function translationMatrix(x: number, y: number, z: number): number[] {
  return [
    1, 0, 0, 0,
    0, 1, 0, 0,
    0, 0, 1, 0,
    x, y, z, 1
  ]
}

/**
 * Mercator 坐标转换
 * 参照原项目的坐标系统
 */
export class MercatorCoordinate {
  x: number
  y: number
  z: number

  constructor(x: number, y: number, z: number = 0) {
    this.x = x
    this.y = y
    this.z = z
  }

  static fromLngLat(lngLat: [number, number], altitude: number = 0): MercatorCoordinate {
    const [lng, lat] = lngLat
    
    // Web Mercator 投影
    const x = (lng + 180) / 360
    const y = (1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2
    
    // 高度转换（简化版）
    const z = altitude / 6378137 // 地球半径
    
    return new MercatorCoordinate(x, y, z)
  }

  toLngLat(): [number, number] {
    const lng = this.x * 360 - 180
    const n = Math.PI - 2 * Math.PI * this.y
    const lat = (180 / Math.PI) * Math.atan(0.5 * (Math.exp(n) - Math.exp(-n)))
    
    return [lng, lat]
  }
}
