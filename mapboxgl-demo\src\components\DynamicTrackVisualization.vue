<template>
  <div id="map-container">
    <div id="map"></div>
    
    <!-- 控制面板 -->
    <div id="controls">
      <h3>🎬 动态轨迹可视化</h3>
      
      <div class="control-group">
        <button @click="loadTrack" :disabled="isLoading">
          {{ isLoading ? '加载中...' : '📁 加载轨迹' }}
        </button>
        <button @click="startAnimation" :disabled="!hasTrack || isPlaying">
          ▶️ 开始动画
        </button>
        <button @click="pauseAnimation" :disabled="!hasTrack || !isPlaying">
          ⏸️ 暂停动画
        </button>
        <button @click="resetAnimation" :disabled="!hasTrack">
          🔄 重置动画
        </button>
      </div>

      <div class="control-group" v-if="hasTrack">
        <label>动画进度</label>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${progress * 100}%` }"></div>
        </div>
        <div class="progress-info">
          <span>{{ (progress * 100).toFixed(1) }}%</span>
          <span>点数: {{ currentPointIndex }}/{{ totalPoints }}</span>
        </div>
      </div>
    </div>

    <!-- 信息面板 -->
    <div id="info-panel" v-if="hasTrack">
      <h4>📍 轨迹信息</h4>
      <p>状态: {{ status }}</p>
      <p>总点数: {{ totalPoints }}</p>
      <p>当前点: {{ currentPointIndex }}</p>
      <p v-if="currentPoint">
        位置: {{ currentPoint.latitude.toFixed(6) }}°, {{ currentPoint.longitude.toFixed(6) }}°
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'

// 响应式数据
const map = ref<mapboxgl.Map>()
const trackPoints = ref<any[]>([])
const isLoading = ref(false)
const hasTrack = ref(false)
const isPlaying = ref(false)
const progress = ref(0)
const currentPointIndex = ref(0)
const totalPoints = ref(0)
const status = ref('等待加载轨迹')
const currentPoint = ref<any>(null)

// 动画相关
let animationId: number | null = null
let startTime: number = 0
const animationDuration = 30000 // 30秒

// Mapbox Access Token (请替换为您的token)
mapboxgl.accessToken = 'pk.eyJ1Ijoiamlhbmd6aGljaGFvIiwiYSI6ImNtZHdqcHp2MTE0a20yaXNpbnc2eXY2bm4ifQ.qbtTOCVw8CgtTeN8S9uuhQ'

// 初始化地图
const initMap = () => {
  map.value = new mapboxgl.Map({
    container: 'map',
    style: 'mapbox://styles/mapbox/satellite-v9',
    center: [10.3090512, 43.9034624],
    zoom: 14,
    pitch: 65,
    bearing: 0,
    antialias: true
  })

  map.value.on('style.load', () => {
    setupTerrain()
    status.value = '地图已加载，点击"加载轨迹"开始'
  })
}

// 设置3D地形
const setupTerrain = () => {
  if (!map.value) return

  map.value.addSource('mapbox-terrain-dem', {
    'type': 'raster-dem',
    'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
    'tileSize': 512,
    'maxzoom': 15
  })

  map.value.setTerrain({
    'source': 'mapbox-terrain-dem',
    'exaggeration': 1
  })

  map.value.addLayer({
    'id': 'sky',
    'type': 'sky',
    'paint': {
      'sky-type': 'atmosphere',
      'sky-atmosphere-sun': [0.0, 90.0],
      'sky-atmosphere-sun-intensity': 15
    }
  })
}

// 加载轨迹数据
const loadTrack = async () => {
  if (!map.value) return

  isLoading.value = true
  status.value = '正在加载轨迹数据...'

  try {
    // 模拟加载GPX数据 - 您可以替换为实际的数据加载逻辑
    const response = await fetch('/mock_data/workout_track.gpx')
    if (!response.ok) throw new Error('Failed to load track data')
    
    // 这里应该解析GPX数据，现在使用模拟数据
    const mockTrackPoints = generateMockTrackPoints()
    
    trackPoints.value = mockTrackPoints
    totalPoints.value = mockTrackPoints.length
    hasTrack.value = true
    
    // 创建轨迹图层
    createTrackLayers()
    
    // 适配地图视图
    fitMapToTrack()
    
    status.value = `成功加载 ${totalPoints.value} 个轨迹点`
    
  } catch (error) {
    console.error('加载轨迹数据失败:', error)
    status.value = '加载失败'
  } finally {
    isLoading.value = false
  }
}

// 生成模拟轨迹数据
const generateMockTrackPoints = () => {
  const points = []
  const startLat = 43.9034624
  const startLng = 10.3090512
  
  for (let i = 0; i < 100; i++) {
    points.push({
      latitude: startLat + (Math.random() - 0.5) * 0.01,
      longitude: startLng + (Math.random() - 0.5) * 0.01,
      elevation: 200 + Math.random() * 100
    })
  }
  
  return points
}

// 创建轨迹图层
const createTrackLayers = () => {
  if (!map.value || !trackPoints.value.length) return

  // 清理已存在的图层
  clearTrackLayers()

  // 创建动态轨迹线（初始为空）
  map.value.addSource('dynamic-track-line', {
    'type': 'geojson',
    'data': {
      'type': 'Feature',
      'properties': {},
      'geometry': {
        'type': 'LineString',
        'coordinates': []
      }
    }
  })

  map.value.addLayer({
    'id': 'dynamic-track-line-layer',
    'type': 'line',
    'source': 'dynamic-track-line',
    'layout': {
      'line-join': 'round',
      'line-cap': 'round'
    },
    'paint': {
      'line-color': '#FF5722',
      'line-width': 6,
      'line-opacity': 0.9
    }
  })

  // 创建当前位置点
  const firstPoint = trackPoints.value[0]
  map.value.addSource('current-position', {
    'type': 'geojson',
    'data': {
      'type': 'Feature',
      'properties': {},
      'geometry': {
        'type': 'Point',
        'coordinates': [firstPoint.longitude, firstPoint.latitude]
      }
    }
  })

  map.value.addLayer({
    'id': 'current-position-point',
    'type': 'circle',
    'source': 'current-position',
    'paint': {
      'circle-radius': 8,
      'circle-color': '#00FF00',
      'circle-stroke-color': '#FFFFFF',
      'circle-stroke-width': 3
    }
  })
}

// 清理轨迹图层
const clearTrackLayers = () => {
  if (!map.value) return

  const layersToRemove = ['dynamic-track-line-layer', 'current-position-point']
  const sourcesToRemove = ['dynamic-track-line', 'current-position']

  layersToRemove.forEach(layerId => {
    if (map.value!.getLayer(layerId)) {
      map.value!.removeLayer(layerId)
    }
  })

  sourcesToRemove.forEach(sourceId => {
    if (map.value!.getSource(sourceId)) {
      map.value!.removeSource(sourceId)
    }
  })
}

// 适配地图视图到轨迹
const fitMapToTrack = () => {
  if (!map.value || !trackPoints.value.length) return

  const coordinates = trackPoints.value.map(point => [point.longitude, point.latitude])
  const bounds = coordinates.reduce((bounds, coord) => {
    return bounds.extend(coord)
  }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]))

  map.value.fitBounds(bounds, {
    padding: 100,
    pitch: 70,
    bearing: 0
  })
}

// 插值计算函数
const getInterpolatedPosition = (progressValue: number) => {
  if (trackPoints.value.length === 0) return null
  if (progressValue <= 0) return trackPoints.value[0]
  if (progressValue >= 1) return trackPoints.value[trackPoints.value.length - 1]

  const totalPointsCount = trackPoints.value.length
  const exactIndex = progressValue * (totalPointsCount - 1)
  const lowerIndex = Math.floor(exactIndex)
  const upperIndex = Math.min(lowerIndex + 1, totalPointsCount - 1)
  const interpolationFactor = exactIndex - lowerIndex

  if (lowerIndex === upperIndex) {
    return trackPoints.value[lowerIndex]
  }

  const lowerPoint = trackPoints.value[lowerIndex]
  const upperPoint = trackPoints.value[upperIndex]

  // 线性插值计算经纬度
  const interpolatedLng = lowerPoint.longitude + (upperPoint.longitude - lowerPoint.longitude) * interpolationFactor
  const interpolatedLat = lowerPoint.latitude + (upperPoint.latitude - lowerPoint.latitude) * interpolationFactor
  const interpolatedElevation = lowerPoint.elevation + (upperPoint.elevation - lowerPoint.elevation) * interpolationFactor

  return {
    longitude: interpolatedLng,
    latitude: interpolatedLat,
    elevation: interpolatedElevation
  }
}

// 更新轨迹可视化 - 修复插值和同步问题
const updateTrackVisualization = (progressValue: number) => {
  if (!map.value || !trackPoints.value.length) return

  const totalPointsCount = trackPoints.value.length
  const currentIndex = Math.floor(progressValue * totalPointsCount)

  console.log(`Updating track visualization: progress=${progressValue.toFixed(3)}, currentPointIndex=${currentIndex}/${totalPointsCount}`)

  // 获取插值位置
  const interpolatedPosition = getInterpolatedPosition(progressValue)

  // 1. 更新轨迹线 - 包含插值点作为终点
  const visiblePoints = trackPoints.value.slice(0, Math.max(1, currentIndex))
  let coordinates = visiblePoints.map((point: any) => [point.longitude, point.latitude])

  // 如果有插值位置且不在轨迹点上，添加插值点作为轨迹线终点
  if (interpolatedPosition && currentIndex < totalPointsCount) {
    const exactIndex = progressValue * (totalPointsCount - 1)
    const interpolationFactor = exactIndex - Math.floor(exactIndex)

    // 只有当插值因子大于0时才添加插值点（避免重复）
    if (interpolationFactor > 0) {
      coordinates.push([interpolatedPosition.longitude, interpolatedPosition.latitude])
    }
  }

  const lineSource = map.value.getSource('dynamic-track-line') as mapboxgl.GeoJSONSource
  if (lineSource) {
    lineSource.setData({
      'type': 'Feature',
      'properties': {},
      'geometry': {
        'type': 'LineString',
        'coordinates': coordinates
      }
    })
  }

  // 2. 更新当前位置点 - 使用插值位置
  if (interpolatedPosition) {
    const pointSource = map.value.getSource('current-position') as mapboxgl.GeoJSONSource
    if (pointSource) {
      pointSource.setData({
        'type': 'Feature',
        'properties': {},
        'geometry': {
          'type': 'Point',
          'coordinates': [interpolatedPosition.longitude, interpolatedPosition.latitude]
        }
      })
    }

    currentPoint.value = interpolatedPosition
  }

  // 更新响应式数据
  progress.value = progressValue
  currentPointIndex.value = currentIndex
}

// 动画循环
const animate = (timestamp: number) => {
  if (!isPlaying.value) return

  if (startTime === 0) {
    startTime = timestamp
  }

  const elapsed = timestamp - startTime
  const progressValue = Math.min(elapsed / animationDuration, 1)

  updateTrackVisualization(progressValue)

  if (progressValue < 1) {
    animationId = requestAnimationFrame(animate)
  } else {
    isPlaying.value = false
    status.value = '动画播放完成'
  }
}

// 开始动画
const startAnimation = () => {
  if (!hasTrack.value) return

  isPlaying.value = true
  status.value = '动画播放中...'
  startTime = 0
  animationId = requestAnimationFrame(animate)
}

// 暂停动画
const pauseAnimation = () => {
  isPlaying.value = false
  if (animationId) {
    cancelAnimationFrame(animationId)
    animationId = null
  }
  status.value = '动画已暂停'
}

// 重置动画
const resetAnimation = () => {
  pauseAnimation()
  progress.value = 0
  currentPointIndex.value = 0
  currentPoint.value = null
  updateTrackVisualization(0)
  status.value = '动画已重置'
}

// 生命周期
onMounted(() => {
  initMap()
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  if (map.value) {
    map.value.remove()
  }
})
</script>

<style scoped>
#map-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

#map {
  width: 100%;
  height: 100%;
}

#controls {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 10px;
  z-index: 100;
  min-width: 250px;
}

#controls h3 {
  margin: 0 0 15px 0;
  color: #4CAF50;
  text-align: center;
}

.control-group {
  margin-bottom: 15px;
}

.control-group button {
  width: 100%;
  padding: 10px;
  margin: 5px 0;
  border: none;
  border-radius: 6px;
  background: #4CAF50;
  color: white;
  cursor: pointer;
  font-weight: bold;
}

.control-group button:hover:not(:disabled) {
  background: #45a049;
}

.control-group button:disabled {
  background: #666;
  cursor: not-allowed;
}

.control-group label {
  display: block;
  margin-bottom: 8px;
  color: #E0E0E0;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  margin: 10px 0;
}

.progress-fill {
  height: 100%;
  background: #4CAF50;
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #E0E0E0;
}

#info-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 15px;
  border-radius: 8px;
  z-index: 100;
  font-family: monospace;
  font-size: 12px;
}

#info-panel h4 {
  margin: 0 0 10px 0;
  color: #4CAF50;
}
</style>
