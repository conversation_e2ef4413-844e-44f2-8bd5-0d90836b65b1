/**
 * 数据加载 Composable
 * 提供响应式的数据加载功能，集成新的 API 服务
 */

import { ref, computed } from 'vue'
import { DataService } from '@/services/DataService'
import { apiService } from '@/services/ApiService'
import type { WorkoutData, ProcessedTrackData, SensorData } from '@/types/track'
import type { ApiResponse, SportsTrackerResponse } from '@/types/api'
import type { ExtractedSensorData, MapVisualizationData } from '@/types/extensions'

export function useDataLoader() {
  const dataService = DataService.getInstance()

  // 响应式状态
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const workoutData = ref<WorkoutData | null>(null)
  const trackData = ref<ProcessedTrackData | null>(null)
  const sensorData = ref<SensorData[]>([])
  const rawResponse = ref<SportsTrackerResponse | null>(null)

  // 新增的响应式状态
  const extractedSensorData = ref<ExtractedSensorData | null>(null)
  const mapVisualizationData = ref<MapVisualizationData | null>(null)
  const workoutSummary = ref<any>(null)

  // 计算属性
  const hasData = computed(() => workoutData.value !== null)
  const hasTrackData = computed(() => trackData.value !== null)
  const hasSensorData = computed(() => sensorData.value.length > 0)
  const hasExtractedData = computed(() => extractedSensorData.value !== null)
  const hasMapData = computed(() => mapVisualizationData.value !== null)

  /**
   * 加载所有数据
   */
  async function loadAllData() {
    isLoading.value = true
    error.value = null

    try {
      // 并行加载所有数据
      const [workoutResponse, trackPoints, sensors] = await Promise.all([
        dataService.loadWorkoutResponse(),
        dataService.loadTrackPoints(),
        dataService.getSensorData()
      ])

      rawResponse.value = workoutResponse
      workoutData.value = await dataService.getWorkoutData()
      sensorData.value = sensors

      // 处理轨迹数据
      if (trackPoints.features && trackPoints.features.length > 0) {
        trackData.value = processTrackData(trackPoints)
      }

      console.log('All data loaded successfully:', {
        workout: workoutData.value,
        trackPoints: trackData.value?.points.length,
        sensorData: sensorData.value.length
      })

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error occurred'
      console.error('Failed to load data:', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 处理轨迹数据，计算边界和统计信息
   */
  function processTrackData(geoJson: GeoJSON.FeatureCollection): ProcessedTrackData {
    const points: any[] = []
    let minLat = Infinity, maxLat = -Infinity
    let minLng = Infinity, maxLng = -Infinity
    let minElevation = Infinity, maxElevation = -Infinity
    let totalDistance = 0
    let elevationGain = 0, elevationLoss = 0

    // 处理GeoJSON数据
    geoJson.features.forEach(feature => {
      if (feature.geometry.type === 'Point') {
        const [lng, lat, elevation] = feature.geometry.coordinates
        const properties = feature.properties || {}

        const point = {
          lat,
          lng,
          elevation: elevation || properties.elevation,
          timestamp: properties.timestamp,
          speed: properties.speed,
          heartRate: properties.heartRate
        }

        points.push(point)

        // 更新边界
        minLat = Math.min(minLat, lat)
        maxLat = Math.max(maxLat, lat)
        minLng = Math.min(minLng, lng)
        maxLng = Math.max(maxLng, lng)

        if (elevation !== undefined) {
          minElevation = Math.min(minElevation, elevation)
          maxElevation = Math.max(maxElevation, elevation)
        }
      } else if (feature.geometry.type === 'LineString') {
        feature.geometry.coordinates.forEach(([lng, lat, elevation], index) => {
          const point = {
            lat,
            lng,
            elevation,
            timestamp: Date.now() + index * 1000, // 模拟时间戳
            speed: 0,
            heartRate: 0
          }

          points.push(point)

          // 更新边界
          minLat = Math.min(minLat, lat)
          maxLat = Math.max(maxLat, lat)
          minLng = Math.min(minLng, lng)
          maxLng = Math.max(maxLng, lng)

          if (elevation !== undefined) {
            minElevation = Math.min(minElevation, elevation)
            maxElevation = Math.max(maxElevation, elevation)
          }
        })
      }
    })

    // 计算高程变化
    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1]
      const curr = points[i]

      if (prev.elevation !== undefined && curr.elevation !== undefined) {
        const diff = curr.elevation - prev.elevation
        if (diff > 0) {
          elevationGain += diff
        } else {
          elevationLoss += Math.abs(diff)
        }
      }

      // 计算距离（简化计算）
      const latDiff = curr.lat - prev.lat
      const lngDiff = curr.lng - prev.lng
      totalDistance += Math.sqrt(latDiff * latDiff + lngDiff * lngDiff) * 111000 // 粗略转换为米
    }

    return {
      points,
      bounds: {
        north: maxLat,
        south: minLat,
        east: maxLng,
        west: minLng
      },
      center: {
        lat: (minLat + maxLat) / 2,
        lng: (minLng + maxLng) / 2
      },
      totalDistance,
      totalTime: points.length > 0 ? (points[points.length - 1].timestamp - points[0].timestamp) / 1000 : 0,
      elevationProfile: {
        min: minElevation === Infinity ? 0 : minElevation,
        max: maxElevation === -Infinity ? 0 : maxElevation,
        gain: elevationGain,
        loss: elevationLoss
      }
    }
  }

  /**
   * 使用新的 API 服务加载数据
   */
  async function loadDataWithApi(workoutId: string = 'default') {
    isLoading.value = true
    error.value = null

    try {
      // 并行加载所有数据
      const [workoutResult, summaryResult, trackResult, sensorResult] = await Promise.all([
        apiService.getWorkout(workoutId),
        apiService.getWorkoutSummary(workoutId),
        apiService.getTrackData(workoutId),
        apiService.getSensorData(workoutId)
      ])

      if (workoutResult.success) {
        rawResponse.value = workoutResult.data
        workoutData.value = await dataService.getWorkoutData()
      }

      if (summaryResult.success) {
        workoutSummary.value = summaryResult.data
      }

      if (trackResult.success) {
        mapVisualizationData.value = trackResult.data
      }

      if (sensorResult.success) {
        extractedSensorData.value = sensorResult.data
      }

      console.log('Data loaded with API service:', {
        workout: !!workoutData.value,
        summary: !!workoutSummary.value,
        track: !!mapVisualizationData.value,
        sensors: !!extractedSensorData.value
      })

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'API 调用失败'
      console.error('Failed to load data with API:', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 加载特定类型的热力图数据
   */
  async function loadHeatmapData(type: 'altitude' | 'heartrate' | 'speed' = 'altitude', workoutId: string = 'default') {
    try {
      const result = await apiService.getHeatmapData(workoutId, type)
      if (result.success) {
        return result.data
      }
      throw new Error(result.message || '加载热力图数据失败')
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载热力图数据失败'
      throw err
    }
  }

  /**
   * 加载心率数据
   */
  async function loadHeartRateData(workoutId: string = 'default') {
    try {
      const result = await apiService.getHeartRateData(workoutId)
      if (result.success) {
        return result.data
      }
      throw new Error(result.message || '加载心率数据失败')
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载心率数据失败'
      throw err
    }
  }

  /**
   * 加载海拔数据
   */
  async function loadAltitudeData(workoutId: string = 'default') {
    try {
      const result = await apiService.getAltitudeData(workoutId)
      if (result.success) {
        return result.data
      }
      throw new Error(result.message || '加载海拔数据失败')
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载海拔数据失败'
      throw err
    }
  }

  /**
   * 加载速度数据
   */
  async function loadSpeedData(workoutId: string = 'default') {
    try {
      const result = await apiService.getSpeedData(workoutId)
      if (result.success) {
        return result.data
      }
      throw new Error(result.message || '加载速度数据失败')
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载速度数据失败'
      throw err
    }
  }

  /**
   * 重新加载数据
   */
  async function reloadData() {
    dataService.clearCache()
    await loadAllData()
  }

  /**
   * 重新加载数据（使用 API 服务）
   */
  async function reloadDataWithApi(workoutId: string = 'default') {
    dataService.clearCache()
    await loadDataWithApi(workoutId)
  }

  /**
   * 获取缓存信息
   */
  function getCacheInfo() {
    return dataService.getCacheInfo()
  }

  return {
    // 状态
    isLoading,
    error,
    workoutData,
    trackData,
    sensorData,
    rawResponse,
    extractedSensorData,
    mapVisualizationData,
    workoutSummary,

    // 计算属性
    hasData,
    hasTrackData,
    hasSensorData,
    hasExtractedData,
    hasMapData,

    // 方法
    loadAllData,
    loadDataWithApi,
    loadHeatmapData,
    loadHeartRateData,
    loadAltitudeData,
    loadSpeedData,
    reloadData,
    reloadDataWithApi,
    getCacheInfo
  }
}
