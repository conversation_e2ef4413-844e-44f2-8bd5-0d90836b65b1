// 基础类型定义
export interface Point2D {
  x: number
  y: number
}

export interface Point3D extends Point2D {
  z: number
}

export interface LatLng {
  lat: number
  lng: number
}

export interface LatLngElevation extends LatLng {
  elevation: number
}

// 运动轨迹相关类型
export interface TrackPoint extends LatLngElevation {
  time: number // 时间戳
  speed?: number // 速度 m/s
  heartRate?: number // 心率
  cadence?: number // 步频/踏频
  power?: number // 功率
  temperature?: number // 温度
}

export interface Track {
  id: string
  name: string
  sport: SportType
  points: TrackPoint[]
  startTime: number
  endTime: number
  totalDistance: number // 总距离（米）
  totalTime: number // 总时间（毫秒）
  maxSpeed: number // 最大速度
  avgSpeed: number // 平均速度
  elevationGain: number // 爬升
  elevationLoss: number // 下降
  maxElevation: number // 最高海拔
  minElevation: number // 最低海拔
}

// 运动类型
export type SportType = 'running' | 'cycling' | 'hiking' | 'skiing' | 'swimming'

// 相机相关类型
export interface CameraPosition {
  center: LatLng
  zoom: number
  bearing: number
  pitch: number
}

export interface CameraPath {
  getPoint(t: number): CameraPosition
  getDuration(): number
}

// WebGL 相关类型
export interface WebGLContext {
  gl: WebGLRenderingContext | WebGL2RenderingContext
  canvas: HTMLCanvasElement
  program: WebGLProgram
}

export interface ShaderSource {
  vertex: string
  fragment: string
}

// 地图样式类型
export type MapStyle = 'streets' | 'outdoors' | 'light' | 'dark' | 'satellite' | 'satellite-streets'

// 播放控制类型
export interface PlaybackControls {
  position: number // 0-1
  isPlaying: boolean
  speed: number
  duration: number
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 配置类型
export interface AppConfig {
  mapboxToken: string
  defaultStyle: MapStyle
  defaultCenter: LatLng
  defaultZoom: number
  animationDuration: number
  maxZoom: number
  minZoom: number
}

// 事件类型
export interface MapEvent {
  type: string
  target: any
  originalEvent?: Event
}

export interface TrackLoadEvent extends MapEvent {
  track: Track
}

export interface PositionChangeEvent extends MapEvent {
  position: number
  time: number
}

// 组件 Props 类型
export interface MapCanvasProps {
  track?: Track
  style?: MapStyle
  controls?: boolean
  autoPlay?: boolean
  loop?: boolean
}

export interface CameraControlsProps {
  position: number
  onPositionChange: (position: number) => void
  isPlaying: boolean
  onTogglePlay: () => void
  speed: number
  onSpeedChange: (speed: number) => void
}

export interface SportSelectorProps {
  selected: SportType
  onSelect: (sport: SportType) => void
  options?: SportType[]
}
