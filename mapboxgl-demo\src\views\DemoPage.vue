<template>
  <div class="demo-page">
    <!-- 演示地图 -->
    <MapCanvas 
      :track="demoTrack"
      :show-controls="true"
      :show-sport-selector="true"
      :auto-play="false"
      style="outdoors"
      @track-loaded="handleTrackLoaded"
      @sport-change="handleSportChange"
      @error="handleError"
    />

    <!-- 演示信息面板 -->
    <div class="demo-info" v-if="showInfo">
      <n-card size="small" class="info-card">
        <template #header>
          <n-space align="center">
            <n-icon size="20" color="#18a058">
              <InfoIcon />
            </n-icon>
            <span>演示信息</span>
          </n-space>
        </template>
        
        <n-space vertical size="small">
          <div class="info-item">
            <strong>轨迹名称:</strong> {{ demoTrack?.name }}
          </div>
          <div class="info-item">
            <strong>运动类型:</strong> {{ $t(`sports.${demoTrack?.sport}`) }}
          </div>
          <div class="info-item">
            <strong>总距离:</strong> {{ formatDistance(demoTrack?.totalDistance || 0) }}
          </div>
          <div class="info-item">
            <strong>总时间:</strong> {{ formatTime(demoTrack?.totalTime || 0) }}
          </div>
          <div class="info-item">
            <strong>平均速度:</strong> {{ formatSpeed(demoTrack?.avgSpeed || 0) }}
          </div>
        </n-space>

        <template #action>
          <n-space>
            <n-button @click="generateNewTrack" size="small">
              生成新轨迹
            </n-button>
            <n-button @click="showInfo = false" size="small" quaternary>
              关闭
            </n-button>
          </n-space>
        </template>
      </n-card>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
      <n-button-group vertical>
        <n-button @click="showInfo = !showInfo" circle>
          <template #icon>
            <n-icon><InfoIcon /></n-icon>
          </template>
        </n-button>
        
        <n-button @click="resetDemo" circle>
          <template #icon>
            <n-icon><RefreshIcon /></n-icon>
          </template>
        </n-button>
      </n-button-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { NCard, NSpace, NIcon, NButton, NButtonGroup } from 'naive-ui'
import { InformationCircle as InfoIcon, Refresh as RefreshIcon } from '@vicons/ionicons5'

import MapCanvas from '@/components/MapCanvas.vue'
import { generateDemoTrack } from '@/utils/demo-data'
import { useMeasurementStore } from '@/stores/measurement'
import { usePositionStore } from '@/stores/position'
import type { Track, SportType } from '@/types'

// Stores
const measurementStore = useMeasurementStore()
const positionStore = usePositionStore()

// 状态
const demoTrack = ref<Track>()
const showInfo = ref(true)

// 格式化函数
const formatDistance = (meters: number) => measurementStore.formatDistance(meters)
const formatSpeed = (metersPerSecond: number) => measurementStore.formatSpeed(metersPerSecond)
const formatTime = (milliseconds: number) => {
  const totalSeconds = Math.floor(milliseconds / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }
}

// 事件处理
const handleTrackLoaded = (track: Track) => {
  console.log('轨迹加载完成:', track)
}

const handleSportChange = (sport: SportType) => {
  console.log('运动类型变更:', sport)
}

const handleError = (error: string) => {
  console.error('地图错误:', error)
}

const generateNewTrack = () => {
  demoTrack.value = generateDemoTrack()
  positionStore.reset()
}

const resetDemo = () => {
  positionStore.reset()
  showInfo.value = true
}

// 初始化
onMounted(() => {
  demoTrack.value = generateDemoTrack()
})
</script>

<style scoped>
.demo-page {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.demo-info {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  max-width: 300px;
}

.info-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.info-item {
  font-size: 14px;
  line-height: 1.5;
}

.floating-actions {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

/* 深色主题适配 */
.dark .info-card {
  background: rgba(0, 0, 0, 0.85);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-info {
    top: 10px;
    left: 10px;
    right: 10px;
    max-width: none;
  }
  
  .floating-actions {
    top: 10px;
    right: 10px;
  }
}
</style>
