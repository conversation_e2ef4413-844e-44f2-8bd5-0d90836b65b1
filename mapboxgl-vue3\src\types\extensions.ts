/**
 * Sports Tracker 扩展数据类型定义
 */

// 海拔数据流
export interface AltitudeStreamExtension {
  type: 'AltitudeStreamExtension'
  values: number[]
  timestamps: number[]
}

// 健身数据
export interface FitnessExtension {
  type: 'FitnessExtension'
  maxHeartRate: number
  vo2Max: number
  estimatedVo2Max: number
}

// 心率数据流
export interface HeartrateStreamExtension {
  type: 'HeartrateStreamExtension'
  values: number[]
  timestamps: number[]
}

// 位置数据流
export interface LocationStreamExtension {
  type: 'LocationStreamExtension'
  locationPoints: LocationPoint[]
}

// 位置点
export interface LocationPoint {
  latitude: number
  longitude: number
  altitude: number
  timestamp: number
}

// 速度数据流
export interface SpeedStreamExtension {
  type: 'SpeedStreamExtension'
  values: number[]
  timestamps: number[]
}

// 摘要扩展
export interface SummaryExtension {
  type: 'SummaryExtension'
  gear: string
  avgTemperature: number
  recoveryTime: number
  trainingStressScore: number
  intensityFactor: number
  normalizedPower: number
  weather: WeatherData
}

// 天气数据
export interface WeatherData {
  temperature: number
  humidity: number
  pressure: number
  windSpeed: number
  windDirection: number
  condition: string
}

// 联合类型
export type Extension = 
  | AltitudeStreamExtension 
  | FitnessExtension 
  | HeartrateStreamExtension 
  | LocationStreamExtension 
  | SpeedStreamExtension 
  | SummaryExtension

// 数据提取辅助类型
export interface ExtractedSensorData {
  altitude: TimeSeriesData
  heartRate: TimeSeriesData
  speed: TimeSeriesData
  location: LocationPoint[]
}

export interface TimeSeriesData {
  values: number[]
  timestamps: number[]
  min: number
  max: number
  avg: number
}

// 地图可视化数据
export interface MapVisualizationData {
  trackPoints: LocationPoint[]
  altitudeHeatmap: HeatmapPoint[]
  heartRateHeatmap: HeatmapPoint[]
  speedHeatmap: HeatmapPoint[]
  startPoint: LocationPoint
  endPoint: LocationPoint
  centerPoint: { latitude: number; longitude: number }
  bounds: {
    north: number
    south: number
    east: number
    west: number
  }
}

export interface HeatmapPoint {
  latitude: number
  longitude: number
  value: number
  normalizedValue: number // 0-1 范围内的标准化值
  timestamp: number
}
