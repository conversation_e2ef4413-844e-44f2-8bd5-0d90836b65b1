import mapboxgl from 'mapbox-gl'
import { LineRenderer } from './LineRenderer'
import { PointRenderer } from './PointRenderer'
import { MercatorCoordinate, multiplyMat4Mat4, buildMatrix } from '@/utils/matrix'
import type { TrackPoint } from '@/types'

/**
 * WebGL LineLayer 实现
 * 参照 maps.suunto.com 的 LineLayer 完整实现
 */
export class LineLayer implements mapboxgl.CustomLayerInterface {
  id: string
  type = 'custom' as const
  renderingMode = '3d' as const

  private map?: mapboxgl.Map
  private pts: number[][] = []
  private lineData?: Float32Array
  private pointData?: Float32Array
  private lineDistData?: Float32Array
  private pointDistData?: Float32Array
  private lineRenderer?: LineRenderer
  private pointRenderer?: PointRenderer
  private tick = 0
  private renderedTick = 0
  private position = 0
  private progressed = false

  // 边界框信息
  private bounds = {
    latMin: 0,
    latMax: 0,
    lonMin: 0,
    lonMax: 0,
    latCenter: 0,
    lonCenter: 0
  }

  // Mercator 坐标系统（参照原项目）
  private xyzMin = { x: 0, y: 0, z: 0 }

  constructor(id: string, trackPoints: TrackPoint[]) {
    this.id = id
    this.convertTrackPoints(trackPoints)
  }

  /**
   * 转换轨迹点数据格式
   * 参照原项目计算累积距离
   */
  private convertTrackPoints(trackPoints: TrackPoint[]): void {
    let cumulativeDistance = 0

    this.pts = trackPoints.map((point, index) => {
      // 计算累积距离（参照原项目）
      if (index > 0) {
        const prev = trackPoints[index - 1]
        const segmentDistance = this.calculateDistance(prev, point)
        cumulativeDistance += segmentDistance
      }

      // 颜色值（0-1，用于渐变）
      const colorValue = Math.min(1, index / trackPoints.length)
      // colorValue 计算完成
      
      return [
        point.lat,
        point.lng,
        colorValue,  // 颜色值
        0,           // 保留字段
        cumulativeDistance  // 累积距离值（公里）
      ]
    })

    console.log('轨迹数据转换完成:', {
      points: this.pts.length,
      totalDistance: cumulativeDistance,
      maxDistance: this.pts[this.pts.length - 1]?.[4] || 0
    })
  }

  /**
   * 计算两点间距离（公里）
   * 参照原项目的距离计算
   */
  private calculateDistance(point1: TrackPoint, point2: TrackPoint): number {
    const R = 6371 // 地球半径（公里）
    const dLat = (point2.lat - point1.lat) * Math.PI / 180
    const dLng = (point2.lng - point1.lng) * Math.PI / 180
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c // 返回公里数
  }

  /**
   * 地图添加时的初始化
   */
  onAdd(map: mapboxgl.Map, gl: WebGLRenderingContext): void {
    this.map = map

    // 预分配缓冲区
    this.lineData = new Float32Array(this.pts.length * 2 * 4)
    this.pointData = new Float32Array(this.pts.length * 6 * 4)
    this.lineDistData = new Float32Array(this.pts.length * 2)
    this.pointDistData = new Float32Array(this.pts.length * 6)

    // 计算边界框
    this.calculateBounds()

    // 创建渲染器
    this.lineRenderer = new LineRenderer(gl)
    this.pointRenderer = new PointRenderer(gl)

    // 初始数据更新
    this.updateData(true)

    console.log('WebGL LineLayer 初始化完成:', {
      points: this.pts.length,
      bounds: this.bounds
    })
  }

  /**
   * 计算边界框和坐标系统
   * 参照原项目设置相对坐标系统
   */
  private calculateBounds(): void {
    let latMin = Infinity
    let latMax = -Infinity
    let lonMin = Infinity
    let lonMax = -Infinity

    for (const pt of this.pts) {
      const [lat, lon] = pt
      if (lon < lonMin) lonMin = lon
      if (lon > lonMax) lonMax = lon
      if (lat < latMin) latMin = lat
      if (lat > latMax) latMax = lat
    }

    this.bounds = {
      latMin,
      latMax,
      lonMin,
      lonMax,
      latCenter: (latMin + latMax) / 2,
      lonCenter: (lonMin + lonMax) / 2
    }

    // 设置相对坐标系统的原点（参照原项目）
    const centerLngLat: [number, number] = [this.bounds.lonCenter, this.bounds.latCenter]
    const centerXyz = MercatorCoordinate.fromLngLat(centerLngLat, 0)
    this.xyzMin = {
      x: centerXyz.x,
      y: centerXyz.y,
      z: 0
    }
  }

  /**
   * 智能数据更新策略
   * 参照原项目的批处理和距离优先更新
   */
  updateData(updateAll?: boolean): void {
    const lineData = this.lineData
    const lineDistData = this.lineDistData
    const pointData = this.pointData
    const pointDistData = this.pointDistData
    const map = this.map

    if (!lineData || !lineDistData || !pointData || !pointDistData || !map) return

    const count = this.pts.length
    const tick = ++this.tick

    // 小数据集全量更新
    if (count < 100) updateAll = true

    // 批处理策略
    const batches: { dist: number; first: number; last: number }[] = []
    const batchCount = updateAll ? 1 : 30
    const center = map.getCenter()

    // 创建批次
    for (let i = 0; i < batchCount; i++) {
      const first = Math.floor((i * count) / batchCount)
      const last = Math.floor(((i + 1) * count) / batchCount)
      
      // 计算批次中心到相机的距离
      const midIndex = Math.floor((first + last) / 2)
      const midPoint = this.pts[midIndex]
      const dist = Math.abs(midPoint[0] - center.lat) + Math.abs(midPoint[1] - center.lng)
      
      batches.push({ dist, first, last })
    }

    // 按距离排序（近距离优先）
    batches.sort((a, b) => a.dist - b.dist)

    // 渐进式更新策略
    for (let i = 0; i < 3; ++i) {
      let batchNum = 0

      if (i === 1) {
        if (updateAll) break
        batchNum = 1 + (tick % 4)  // 次近批次轮流更新
      } else if (i === 2) {
        batchNum = 5 + (tick % (batchCount - 5))  // 远距离批次低频更新
      }

      const batch = batches[batchNum]
      this.updateBatch(batch.first, batch.last, lineData, pointData, lineDistData, pointDistData)
    }
  }

  /**
   * 更新单个批次的数据
   * 参照原项目使用 Mercator 坐标系统
   */
  private updateBatch(
    first: number,
    last: number,
    lineData: Float32Array,
    pointData: Float32Array,
    lineDistData: Float32Array,
    pointDistData: Float32Array
  ): void {
    for (let i = first; i < last; i++) {
      const pt = this.pts[i]
      const [lat, lon, colorValue, , distance] = pt

      // 严格按照原项目的坐标转换实现
      const ll = {
        lng: lon,
        lat: lat
      }

      // 查询地形高度（简化版，暂时使用0）
      let elevation = 0 // map.queryTerrainElevation(ll, { exaggerated: true })
      const xyz = MercatorCoordinate.fromLngLat([lon, lat], elevation || 0)

      // 关键：使用相对坐标系统（参照原项目第450-451行）
      const x = xyz.x - this.xyzMin.x
      const y = xyz.y - this.xyzMin.y
      const z = xyz.z || 0

      // 线条数据（每个点生成两个顶点）
      const lineIndex = i * 8
      lineData[lineIndex] = x
      lineData[lineIndex + 1] = y
      lineData[lineIndex + 2] = z
      lineData[lineIndex + 3] = 2 + colorValue  // item < 3.0

      lineData[lineIndex + 4] = x
      lineData[lineIndex + 5] = y
      lineData[lineIndex + 6] = z
      lineData[lineIndex + 7] = 3 + colorValue  // item >= 3.0

      // 点数据（每个点生成6个顶点，形成两个三角形）
      const pointIndex = i * 24
      for (let j = 0; j < 6; j++) {
        const idx = pointIndex + j * 4
        pointData[idx] = x
        pointData[idx + 1] = y
        pointData[idx + 2] = z
        pointData[idx + 3] = 2 + j + colorValue  // 不同的 item 值
      }

      // 距离数据
      const lineDistIndex = i * 2
      lineDistData[lineDistIndex] = distance
      lineDistData[lineDistIndex + 1] = distance

      const pointDistIndex = i * 6
      for (let j = 0; j < 6; j++) {
        pointDistData[pointDistIndex + j] = distance
      }
    }
  }

  /**
   * 渲染方法
   * 参照原项目应用本地坐标变换
   */
  render(gl: WebGLRenderingContext, matrix: number[]): void {
    if (!this.lineData || !this.lineDistData || !this.pointData || !this.pointDistData ||
        !this.lineRenderer || !this.pointRenderer) {
      return
    }

    // 更新 GPU 缓冲区
    if (this.tick > this.renderedTick) {
      this.lineRenderer.update(gl, this.lineData, this.pts.length)
      this.lineRenderer.updateDist(gl, this.lineDistData)
      this.pointRenderer.update(gl, this.pointData, this.pts.length)
      this.pointRenderer.updateDist(gl, this.pointDistData)
      this.renderedTick = this.tick
    }

    // 严格按照原项目的矩阵变换（第498-506行）
    let transformedMatrix = multiplyMat4Mat4(
      matrix,
      buildMatrix(
        [1, 0, 0, 0],
        [0, 1, 0, 0],
        [0, 0, 1, 0],
        [this.xyzMin.x, this.xyzMin.y, 0, 1]
      )
    )

    // 原项目的矩阵缩放处理（第508-512行）
    const scaleFactor = 1 / Math.max.apply(null, transformedMatrix.map(Math.abs))
    for (let i = 0; i < 16; ++i) {
      transformedMatrix[i] *= scaleFactor
    }

    // 计算渲染参数
    const position = this.progressed ? this.position : this.pts[this.pts.length - 1][4]
    const thickness = 0.01  // 大幅增加厚度确保可见

    // 严格按照原项目的相机参数计算，确保始终有有效值
    const zoom = this.map && this.map.getZoom()
    const xyz = this.map && this.map.getFreeCameraOptions().position
    const pitch = this.map && Math.tan((this.map.getPitch() / 180) * Math.PI)

    // 确保相机参数始终有效
    let camera: number[]
    if (xyz) {
      camera = [xyz.x - this.xyzMin.x, xyz.y - this.xyzMin.y, xyz.z || 0, pitch || 0]
    } else {
      // 默认相机参数
      camera = [0, 0, 0, 0]
    }

    // 调试日志
    if (Math.random() < 0.01) { // 1% 概率输出调试信息
      console.log('WebGL LineLayer 渲染:', {
        position,
        thickness,
        pointCount: this.pts.length,
        progressed: this.progressed,
        hasData: !!this.lineData,
        bounds: this.bounds,
        xyzMin: this.xyzMin,
        camera,
        sampleCoord: this.lineData ? [this.lineData[0], this.lineData[1], this.lineData[2]] : null,
        matrixSample: transformedMatrix.slice(0, 4)
      })
    }

    // 渲染线条和点
    this.lineRenderer.render(gl, transformedMatrix, thickness, position, camera)
    this.pointRenderer.render(gl, transformedMatrix, thickness, position, camera)
  }

  /**
   * 设置播放进度
   * 参照原项目：直接接收距离值而不是 0-1 进度
   */
  setPosition(distanceKm: number): void {
    this.position = distanceKm
    this.progressed = distanceKm > 0

    // 触发重新渲染
    if (this.map) {
      this.map.triggerRepaint()
    }
  }

  /**
   * 清理资源
   */
  onRemove(): void {
    if (this.lineRenderer && this.pointRenderer) {
      // 注意：这里需要 WebGL 上下文，但 onRemove 没有提供
      // 实际项目中可能需要在其他地方清理
      console.log('WebGL LineLayer 资源清理')
    }
  }
}
