/**
 * 数据服务 - 模拟API调用，从本地文件加载数据
 * 基于 Sports Tracker response.json 数据结构
 */

import type { WorkoutData, TrackPoint, SensorData } from '@/types/track'
import type { ApiResponse, SportsTrackerResponse } from '@/types/api'
import type {
  ExtractedSensorData,
  MapVisualizationData,
  LocationPoint,
  TimeSeriesData,
  HeatmapPoint
} from '@/types/extensions'

export class DataService {
  private static instance: DataService
  private cache = new Map<string, any>()

  static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService()
    }
    return DataService.instance
  }

  /**
   * 加载运动数据响应 - 返回完整的 Sports Tracker 响应
   */
  async loadWorkoutResponse(): Promise<SportsTrackerResponse> {
    const cacheKey = 'workout-response'

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch('/mock-data/response.json')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error('Failed to load workout response:', error)
      // 返回模拟数据作为fallback
      const fallbackData: SportsTrackerResponse = {
        version: 6,
        camera: {
          center: [10.30905, 43.90346],
          zoom: 14,
          bearing: 0,
          pitch: 60,
          duration: 40000,
          essential: true
        },
        workout: {
          feedType: 'WORKOUT',
          username: 'testuser',
          fullname: 'Test User',
          activityId: 1,
          key: 'test-workout-key',
          workoutKey: 'test-workout-key',
          startTime: Date.now() - 2400000,
          stopTime: Date.now(),
          totalTime: 2400,
          totalDistance: 8500,
          startPosition: { x: 10.30905, y: 43.90346 },
          stopPosition: { x: 10.30881, y: 43.90339 },
          centerPosition: { x: 10.30025, y: 43.90832 },
          maxSpeed: 4.2,
          polyline: 'test-polyline',
          avgPace: 4.7,
          avgSpeed: 3.5,
          hrdata: {
            workoutMaxHR: 185,
            workoutAvgHR: 162,
            userMaxHR: 190,
            hrZones: [0, 0, 15, 20, 5]
          },
          energyConsumption: 650,
          cadence: {
            workoutMaxCadence: 180,
            workoutAvgCadence: 165
          },
          stepCount: 12000,
          totalAscent: 120,
          totalDescent: 115,
          recoveryTime: 64800,
          rankings: [],
          userPhoto: '',
          coverPhoto: ''
        },
        extensions: [],
        minAltitude: 170,
        maxAltitude: 269,
        tss: {
          type: 'heartrate',
          trainingStressScore: 85
        },
        tssList: [],
        zoneSense: null,
        suuntoTags: [],
        achievements: []
      }
      this.cache.set(cacheKey, fallbackData)
      return fallbackData
    }
  }

  /**
   * 加载轨迹坐标点 (GeoJSON格式)
   */
  async loadTrackPoints(): Promise<GeoJSON.FeatureCollection> {
    const cacheKey = 'track-points'

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch('/geo-data/workout_track.geojson')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error('Failed to load track points:', error)
      throw new Error('Failed to load track points')
    }
  }

  /**
   * 加载CSV格式的轨迹数据
   */
  async loadTrackCSV(): Promise<string> {
    const cacheKey = 'track-csv'

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch('/geo-data/workout_track.csv')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.text()
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error('Failed to load track CSV:', error)
      throw new Error('Failed to load track CSV')
    }
  }

  /**
   * 加载GPX格式的轨迹数据
   */
  async loadTrackGPX(): Promise<string> {
    const cacheKey = 'track-gpx'

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch('/geo-data/workout_track.gpx')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.text()
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error('Failed to load track GPX:', error)
      throw new Error('Failed to load track GPX')
    }
  }

  /**
   * 解析运动数据，提取核心信息
   */
  async getWorkoutData(): Promise<WorkoutData> {
    const response = await this.loadWorkoutResponse()
    const workout = response.workout

    return {
      id: workout.workoutKey,
      name: `${workout.fullname} 的运动`,
      type: this.getActivityTypeName(workout.activityId),
      date: new Date(workout.startTime).toISOString(),
      duration: workout.totalTime,
      distance: workout.totalDistance,
      elevationGain: workout.totalAscent,
      elevationLoss: workout.totalDescent,
      startLocation: {
        lat: workout.startPosition.y,
        lng: workout.startPosition.x,
        name: '起点'
      },
      endLocation: {
        lat: workout.stopPosition.y,
        lng: workout.stopPosition.x,
        name: '终点'
      },
      animation: {
        duration: 40000, // 40秒动画
        fps: 60,
        totalFrames: 2400,
        cameraSettings: {
          followTrack: true,
          smoothing: 0.8,
          heightOffset: 200,
          lookAhead: 0.1
        }
      },
      rendering: {
        trackColor: {
          start: '#00ff00',
          end: '#ff0000',
          encoding: 'speed'
        },
        lineWidth: 4,
        opacity: 0.8,
        shadows: true
      },
      terrain: {
        enabled: true,
        exaggeration: 2.0,
        source: 'mapbox-dem',
        varianceImageUrl: '/dem_std_zoom_4_512.png'
      }
    }
  }

  /**
   * 获取活动类型名称
   */
  private getActivityTypeName(activityId: number): string {
    const activityTypes: Record<number, string> = {
      1: '跑步',
      2: '骑行',
      3: '游泳',
      4: '徒步',
      5: '滑雪'
    }
    return activityTypes[activityId] || '未知运动'
  }

  /**
   * 获取传感器数据（从response.json中提取）
   */
  async getSensorData(): Promise<SensorData[]> {
    const response = await this.loadWorkoutResponse()
    const extensions = response.extensions

    if (!extensions || extensions.length === 0) {
      return []
    }

    const sensorData: SensorData[] = []

    // 提取高程数据
    const altitudeExt = extensions.find((ext: any) => ext.type === 'AltitudeStreamExtension')
    if (altitudeExt && altitudeExt.values && altitudeExt.timestamps) {
      altitudeExt.values.forEach((value: number, index: number) => {
        sensorData.push({
          timestamp: altitudeExt.timestamps[index],
          type: 'altitude',
          value: value
        })
      })
    }

    // 提取心率数据
    const hrExt = extensions.find((ext: any) => ext.type === 'HeartrateStreamExtension')
    if (hrExt && hrExt.values && hrExt.timestamps) {
      hrExt.values.forEach((value: number, index: number) => {
        sensorData.push({
          timestamp: hrExt.timestamps[index],
          type: 'heartrate',
          value: value
        })
      })
    }

    // 提取速度数据
    const speedExt = extensions.find((ext: any) => ext.type === 'SpeedStreamExtension')
    if (speedExt && speedExt.values && speedExt.timestamps) {
      speedExt.values.forEach((value: number, index: number) => {
        sensorData.push({
          timestamp: speedExt.timestamps[index],
          type: 'speed',
          value: value
        })
      })
    }

    return sensorData
  }

  /**
   * 提取所有扩展数据，按类型分组
   */
  async getExtractedSensorData(): Promise<ExtractedSensorData> {
    const response = await this.loadWorkoutResponse()
    const extensions = response.extensions

    const result: ExtractedSensorData = {
      altitude: { values: [], timestamps: [], min: 0, max: 0, avg: 0 },
      heartRate: { values: [], timestamps: [], min: 0, max: 0, avg: 0 },
      speed: { values: [], timestamps: [], min: 0, max: 0, avg: 0 },
      location: []
    }

    if (!extensions) return result

    // 提取高程数据
    const altitudeExt = extensions.find((ext: any) => ext.type === 'AltitudeStreamExtension')
    if (altitudeExt) {
      result.altitude = this.processTimeSeriesData(altitudeExt.values, altitudeExt.timestamps)
    }

    // 提取心率数据
    const hrExt = extensions.find((ext: any) => ext.type === 'HeartrateStreamExtension')
    if (hrExt) {
      result.heartRate = this.processTimeSeriesData(hrExt.values, hrExt.timestamps)
    }

    // 提取速度数据
    const speedExt = extensions.find((ext: any) => ext.type === 'SpeedStreamExtension')
    if (speedExt) {
      result.speed = this.processTimeSeriesData(speedExt.values, speedExt.timestamps)
    }

    // 提取位置数据
    const locationExt = extensions.find((ext: any) => ext.type === 'LocationStreamExtension')
    if (locationExt && locationExt.locationPoints) {
      result.location = locationExt.locationPoints
    }

    return result
  }

  /**
   * 处理时间序列数据，计算统计信息
   */
  private processTimeSeriesData(values: number[], timestamps: number[]): TimeSeriesData {
    if (!values || values.length === 0) {
      return { values: [], timestamps: [], min: 0, max: 0, avg: 0 }
    }

    const min = Math.min(...values)
    const max = Math.max(...values)
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length

    return {
      values,
      timestamps: timestamps || [],
      min,
      max,
      avg
    }
  }

  /**
   * 获取地图可视化数据
   */
  async getMapVisualizationData(): Promise<MapVisualizationData> {
    const response = await this.loadWorkoutResponse()
    const sensorData = await this.getExtractedSensorData()
    const workout = response.workout

    const trackPoints = sensorData.location

    // 计算边界
    const bounds = this.calculateBounds(trackPoints)

    // 创建热力图数据
    const altitudeHeatmap = this.createHeatmapData(trackPoints, sensorData.altitude)
    const heartRateHeatmap = this.createHeatmapData(trackPoints, sensorData.heartRate)
    const speedHeatmap = this.createHeatmapData(trackPoints, sensorData.speed)

    return {
      trackPoints,
      altitudeHeatmap,
      heartRateHeatmap,
      speedHeatmap,
      startPoint: trackPoints[0] || { latitude: workout.startPosition.y, longitude: workout.startPosition.x, altitude: 0, timestamp: workout.startTime },
      endPoint: trackPoints[trackPoints.length - 1] || { latitude: workout.stopPosition.y, longitude: workout.stopPosition.x, altitude: 0, timestamp: workout.stopTime },
      centerPoint: { latitude: workout.centerPosition.y, longitude: workout.centerPosition.x },
      bounds
    }
  }

  /**
   * 计算轨迹边界
   */
  private calculateBounds(points: LocationPoint[]) {
    if (points.length === 0) {
      return { north: 0, south: 0, east: 0, west: 0 }
    }

    let north = points[0].latitude
    let south = points[0].latitude
    let east = points[0].longitude
    let west = points[0].longitude

    points.forEach(point => {
      north = Math.max(north, point.latitude)
      south = Math.min(south, point.latitude)
      east = Math.max(east, point.longitude)
      west = Math.min(west, point.longitude)
    })

    return { north, south, east, west }
  }

  /**
   * 创建热力图数据
   */
  private createHeatmapData(trackPoints: LocationPoint[], sensorData: TimeSeriesData): HeatmapPoint[] {
    if (trackPoints.length === 0 || sensorData.values.length === 0) {
      return []
    }

    const { min, max } = sensorData
    const range = max - min

    return trackPoints.map((point, index) => {
      const value = sensorData.values[index] || 0
      const normalizedValue = range > 0 ? (value - min) / range : 0

      return {
        latitude: point.latitude,
        longitude: point.longitude,
        value,
        normalizedValue,
        timestamp: point.timestamp
      }
    })
  }

  /**
   * 获取运动统计摘要
   */
  async getWorkoutSummary() {
    const response = await this.loadWorkoutResponse()
    const workout = response.workout
    const sensorData = await this.getExtractedSensorData()

    return {
      // 基本信息
      athlete: workout.fullname,
      activityType: this.getActivityTypeName(workout.activityId),
      startTime: new Date(workout.startTime).toLocaleString(),
      duration: this.formatDuration(workout.totalTime),

      // 距离和速度
      distance: `${(workout.totalDistance / 1000).toFixed(2)} km`,
      avgSpeed: `${workout.avgSpeed.toFixed(1)} m/s`,
      maxSpeed: `${workout.maxSpeed.toFixed(1)} m/s`,
      avgPace: `${workout.avgPace.toFixed(1)} min/km`,

      // 高程
      elevationGain: `${workout.totalAscent} m`,
      elevationLoss: `${workout.totalDescent} m`,
      minAltitude: `${response.minAltitude} m`,
      maxAltitude: `${response.maxAltitude} m`,

      // 心率
      avgHeartRate: `${workout.hrdata.workoutAvgHR} bpm`,
      maxHeartRate: `${workout.hrdata.workoutMaxHR} bpm`,

      // 其他
      calories: `${workout.energyConsumption} kcal`,
      steps: workout.stepCount.toLocaleString(),
      avgCadence: `${workout.cadence.workoutAvgCadence} spm`,
      recoveryTime: this.formatDuration(workout.recoveryTime),

      // 训练评分
      tss: response.tss.trainingStressScore,

      // 成就
      achievements: response.achievements,
      suuntoTags: response.suuntoTags
    }
  }

  /**
   * 格式化时长
   */
  private formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * 获取缓存状态
   */
  getCacheInfo(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}
