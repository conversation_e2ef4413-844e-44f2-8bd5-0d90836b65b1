<template>
  <div class="map-container">
    <div id="mapbox-map" class="map"></div>
    <div class="map-overlay">
      <div class="map-info">
        <span class="map-status">{{ mapStatus }}</span>
        <span v-if="error" class="map-error">{{ error }}</span>
        <span v-if="terrainError" class="map-error">地形错误: {{ terrainError }}</span>
      </div>

      <!-- 地形信息显示 -->
      <div v-if="isTerrainEnabled && currentTerrainInfo" class="terrain-info">
        <div class="terrain-item">
          <span class="terrain-label">地形源:</span>
          <span class="terrain-value">{{ currentTerrainInfo.source }}</span>
        </div>
        <div class="terrain-item">
          <span class="terrain-label">夸张度:</span>
          <span class="terrain-value">{{ currentTerrainInfo.exaggeration.toFixed(2) }}</span>
        </div>
        <div class="terrain-item">
          <span class="terrain-label">缩放:</span>
          <span class="terrain-value">{{ currentTerrainInfo.zoom.toFixed(1) }}</span>
        </div>
      </div>

      <!-- 地形测试按钮 -->
      <div class="terrain-test-buttons">
        <button @click="testTerrain" class="test-btn">测试地形</button>
        <button @click="toggleTerrainTest" class="test-btn">切换地形</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue'
import mapboxgl from 'mapbox-gl'
import { mapboxConfig, validateConfig } from '@/config/mapbox'
import { useDataLoader } from '@/composables/useDataLoader'
import { useTerrain } from '@/composables/useTerrain'
import { debugResponseData, debugMapVisualizationData } from '@/utils/debugData'

// 响应式状态
const mapStatus = ref('地图初始化中...')
const error = ref<string | null>(null)
const mapInstance = ref<mapboxgl.Map | null>(null)
const terrainUpdateInterval = ref<number | null>(null)

// 数据加载器
const { loadDataWithApi, mapVisualizationData, isLoading } = useDataLoader()

// 地形管理器
const {
  initializeTerrain,
  isTerrainEnabled,
  currentTerrainInfo,
  error: terrainError,
  updateTerrainInfo,
  toggleTerrain,
  setTerrainExaggeration
} = useTerrain('/dem_std_zoom_4_512.png')

onMounted(async () => {
  try {
    // 验证配置
    if (!validateConfig()) {
      throw new Error('Mapbox配置验证失败，请检查环境变量')
    }

    // 设置访问令牌
    mapboxgl.accessToken = mapboxConfig.accessToken

    mapStatus.value = '创建地图实例...'

    // 创建地图实例
    const map = new mapboxgl.Map({
      container: 'mapbox-map',
      style: mapboxConfig.style,
      center: [24.9384, 60.1699], // 默认中心点 (赫尔辛基)
      zoom: 10,
      pitch: 45,
      bearing: 0,
      antialias: true
    })

    mapInstance.value = map

    // 地图加载完成事件
    map.on('load', async () => {
      try {
        mapStatus.value = '初始化地形系统...'

        // 初始化地形系统
        console.log('开始初始化地形系统，地图实例:', !!map)
        await initializeTerrain(map)
        console.log('地形系统初始化完成')

        // 验证地形是否已应用
        setTimeout(() => {
          const terrain = map.getTerrain()
          console.log('当前地形设置:', terrain)
          if (terrain) {
            console.log('地形已成功应用:', {
              source: terrain.source,
              exaggeration: terrain.exaggeration
            })
          } else {
            console.warn('地形未应用')
          }
        }, 500)

        mapStatus.value = '加载轨迹数据...'

        // 调试原始数据
        const rawData = await debugResponseData()

        // 加载数据
        await loadDataWithApi()

        console.log('数据加载完成:', {
          hasMapData: !!mapVisualizationData.value,
          trackPointsCount: mapVisualizationData.value?.trackPoints?.length || 0,
          bounds: mapVisualizationData.value?.bounds
        })

        // 调试地图可视化数据
        debugMapVisualizationData(mapVisualizationData.value)

        // 如果有地图可视化数据，调整视图
        if (mapVisualizationData.value && mapVisualizationData.value.trackPoints && mapVisualizationData.value.trackPoints.length > 0) {
          const bounds = new mapboxgl.LngLatBounds()
          let validPointsCount = 0

          mapVisualizationData.value.trackPoints.forEach(point => {
            // 验证坐标有效性
            if (point.longitude && point.latitude &&
                !isNaN(point.longitude) && !isNaN(point.latitude) &&
                point.longitude >= -180 && point.longitude <= 180 &&
                point.latitude >= -90 && point.latitude <= 90) {
              bounds.extend([point.longitude, point.latitude])
              validPointsCount++
            }
          })

          // 只有在有有效坐标点时才调整视图
          if (validPointsCount > 0) {
            map.fitBounds(bounds, {
              padding: 50,
              duration: 2000
            })
            console.log(`地图视图已调整，包含 ${validPointsCount} 个有效轨迹点`)
          } else {
            console.warn('没有找到有效的轨迹点坐标')
          }
        } else {
          console.warn('没有可用的地图可视化数据')
        }

        mapStatus.value = '地图已就绪'
        console.log('Mapbox地图初始化完成')

        // 设置地形信息更新定时器
        terrainUpdateInterval.value = window.setInterval(() => {
          updateTerrainInfo()
        }, 2000) // 每2秒更新一次地形信息

      } catch (err) {
        console.error('地图配置失败:', err)
        error.value = err instanceof Error ? err.message : '地图配置失败'
        mapStatus.value = '地图配置失败'
      }
    })

    // 地图错误事件
    map.on('error', (e) => {
      console.error('Mapbox地图错误:', e.error)
      error.value = e.error.message
      mapStatus.value = '地图加载失败'
    })

  } catch (err) {
    console.error('地图初始化失败:', err)
    error.value = err instanceof Error ? err.message : '地图初始化失败'
    mapStatus.value = '地图初始化失败'
  }
})

/**
 * 测试地形功能
 */
function testTerrain() {
  if (mapInstance.value) {
    console.log('=== 地形测试 ===')
    console.log('地图实例:', !!mapInstance.value)
    console.log('样式已加载:', mapInstance.value.isStyleLoaded())

    // 检查数据源
    const mapboxDem = mapInstance.value.getSource('mapbox-dem')
    const mmlDem = mapInstance.value.getSource('mml-dem')
    console.log('数据源状态:', {
      mapboxDem: !!mapboxDem,
      mmlDem: !!mmlDem
    })

    // 检查当前地形
    const currentTerrain = mapInstance.value.getTerrain()
    console.log('当前地形:', currentTerrain)

    // 手动设置地形
    if (mapboxDem) {
      console.log('手动设置地形...')
      mapInstance.value.setTerrain({
        source: 'mapbox-dem',
        exaggeration: 2.0
      })

      setTimeout(() => {
        const newTerrain = mapInstance.value?.getTerrain()
        console.log('设置后的地形:', newTerrain)
      }, 1000)
    }
  }
}

/**
 * 切换地形测试
 */
function toggleTerrainTest() {
  toggleTerrain()
}

onUnmounted(() => {
  // 清理定时器
  if (terrainUpdateInterval.value) {
    window.clearInterval(terrainUpdateInterval.value)
    terrainUpdateInterval.value = null
  }

  // 清理地图实例
  if (mapInstance.value) {
    mapInstance.value.remove()
    mapInstance.value = null
  }
})
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.map {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  color: #333333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
  min-width: 200px;
}

.map-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.map-status {
  font-weight: 600;
  color: #2c3e50;
}

.map-error {
  color: #e74c3c;
  font-weight: 500;
}

.terrain-info {
  border-top: 1px solid #e9ecef;
  padding-top: 8px;
  margin-top: 8px;
}

.terrain-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.terrain-label {
  font-weight: 500;
  color: #495057;
  font-size: 11px;
}

.terrain-value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 11px;
  font-family: 'Courier New', monospace;
}

.terrain-test-buttons {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.test-btn {
  padding: 4px 8px;
  border: 1px solid #007bff;
  border-radius: 4px;
  background: white;
  color: #007bff;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.3s;
}

.test-btn:hover {
  background: #007bff;
  color: white;
}
</style>
