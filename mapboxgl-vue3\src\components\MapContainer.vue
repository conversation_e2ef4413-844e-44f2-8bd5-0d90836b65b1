<template>
  <div class="map-container">
    <div id="mapbox-map" class="map"></div>
    <div class="map-overlay">
      <div class="map-info">
        <span class="map-status">{{ mapStatus }}</span>
        <span v-if="error" class="map-error">{{ error }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import mapboxgl from 'mapbox-gl'
import { mapboxConfig, validateConfig, getTerrainSourceConfig, getTerrainLayerConfig } from '@/config/mapbox'
import { useDataLoader } from '@/composables/useDataLoader'

// 响应式状态
const mapStatus = ref('地图初始化中...')
const error = ref<string | null>(null)
const mapInstance = ref<mapboxgl.Map | null>(null)

// 数据加载器
const { loadAllData, trackData, isLoading } = useDataLoader()

onMounted(async () => {
  try {
    // 验证配置
    if (!validateConfig()) {
      throw new Error('Mapbox配置验证失败，请检查环境变量')
    }

    // 设置访问令牌
    mapboxgl.accessToken = mapboxConfig.accessToken

    mapStatus.value = '创建地图实例...'

    // 创建地图实例
    const map = new mapboxgl.Map({
      container: 'mapbox-map',
      style: mapboxConfig.style,
      center: [24.9384, 60.1699], // 默认中心点 (赫尔辛基)
      zoom: 10,
      pitch: 45,
      bearing: 0,
      antialias: true
    })

    mapInstance.value = map

    // 地图加载完成事件
    map.on('load', async () => {
      try {
        mapStatus.value = '配置地形...'

        // 添加地形数据源
        map.addSource('mapbox-dem', getTerrainSourceConfig())

        // 设置地形
        map.setTerrain(getTerrainLayerConfig())

        mapStatus.value = '加载轨迹数据...'

        // 加载数据
        await loadAllData()

        // 如果有轨迹数据，调整视图
        if (trackData.value) {
          const bounds = new mapboxgl.LngLatBounds()
          trackData.value.points.forEach(point => {
            bounds.extend([point.lng, point.lat])
          })

          map.fitBounds(bounds, {
            padding: 50,
            duration: 2000
          })
        }

        mapStatus.value = '地图已就绪'
        console.log('Mapbox地图初始化完成')

      } catch (err) {
        console.error('地图配置失败:', err)
        error.value = err instanceof Error ? err.message : '地图配置失败'
        mapStatus.value = '地图配置失败'
      }
    })

    // 地图错误事件
    map.on('error', (e) => {
      console.error('Mapbox地图错误:', e.error)
      error.value = e.error.message
      mapStatus.value = '地图加载失败'
    })

  } catch (err) {
    console.error('地图初始化失败:', err)
    error.value = err instanceof Error ? err.message : '地图初始化失败'
    mapStatus.value = '地图初始化失败'
  }
})

onUnmounted(() => {
  // 清理地图实例
  if (mapInstance.value) {
    mapInstance.value.remove()
    mapInstance.value = null
  }
})
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.map {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #666666;
}

.map-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.map-status {
  font-weight: 500;
}
</style>
