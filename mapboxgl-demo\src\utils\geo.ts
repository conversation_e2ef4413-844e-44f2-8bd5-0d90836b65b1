import type { LatLng, TrackPoint } from '@/types'

// 地理计算工具函数

/**
 * 地球半径（米）
 */
export const EARTH_RADIUS = 6371000

/**
 * 计算两个经纬度点之间的距离（Haversine公式）
 */
export const calculateDistance = (point1: LatLng, point2: LatLng): number => {
  const lat1Rad = (point1.lat * Math.PI) / 180
  const lat2Rad = (point2.lat * Math.PI) / 180
  const deltaLatRad = ((point2.lat - point1.lat) * Math.PI) / 180
  const deltaLngRad = ((point2.lng - point1.lng) * Math.PI) / 180

  const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) *
    Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2)
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  
  return EARTH_RADIUS * c
}

/**
 * 计算轨迹总距离
 */
export const calculateTrackDistance = (points: TrackPoint[]): number => {
  if (points.length < 2) return 0
  
  let totalDistance = 0
  for (let i = 1; i < points.length; i++) {
    totalDistance += calculateDistance(points[i - 1], points[i])
  }
  
  return totalDistance
}

/**
 * 计算两点之间的方位角（度）
 */
export const calculateBearing = (point1: LatLng, point2: LatLng): number => {
  const lat1Rad = (point1.lat * Math.PI) / 180
  const lat2Rad = (point2.lat * Math.PI) / 180
  const deltaLngRad = ((point2.lng - point1.lng) * Math.PI) / 180

  const y = Math.sin(deltaLngRad) * Math.cos(lat2Rad)
  const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) -
    Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(deltaLngRad)

  const bearingRad = Math.atan2(y, x)
  const bearingDeg = (bearingRad * 180) / Math.PI

  return (bearingDeg + 360) % 360
}

/**
 * 根据起点、距离和方位角计算终点
 */
export const calculateDestination = (
  start: LatLng,
  distance: number,
  bearing: number
): LatLng => {
  const bearingRad = (bearing * Math.PI) / 180
  const lat1Rad = (start.lat * Math.PI) / 180
  const lng1Rad = (start.lng * Math.PI) / 180

  const lat2Rad = Math.asin(
    Math.sin(lat1Rad) * Math.cos(distance / EARTH_RADIUS) +
    Math.cos(lat1Rad) * Math.sin(distance / EARTH_RADIUS) * Math.cos(bearingRad)
  )

  const lng2Rad = lng1Rad + Math.atan2(
    Math.sin(bearingRad) * Math.sin(distance / EARTH_RADIUS) * Math.cos(lat1Rad),
    Math.cos(distance / EARTH_RADIUS) - Math.sin(lat1Rad) * Math.sin(lat2Rad)
  )

  return {
    lat: (lat2Rad * 180) / Math.PI,
    lng: (lng2Rad * 180) / Math.PI
  }
}

/**
 * 计算轨迹的边界框
 */
export const calculateBounds = (points: LatLng[]): {
  north: number
  south: number
  east: number
  west: number
} => {
  if (points.length === 0) {
    return { north: 0, south: 0, east: 0, west: 0 }
  }

  let north = points[0].lat
  let south = points[0].lat
  let east = points[0].lng
  let west = points[0].lng

  for (const point of points) {
    north = Math.max(north, point.lat)
    south = Math.min(south, point.lat)
    east = Math.max(east, point.lng)
    west = Math.min(west, point.lng)
  }

  return { north, south, east, west }
}

/**
 * 计算轨迹中心点
 */
export const calculateCenter = (points: LatLng[]): LatLng => {
  if (points.length === 0) {
    return { lat: 0, lng: 0 }
  }

  const bounds = calculateBounds(points)
  return {
    lat: (bounds.north + bounds.south) / 2,
    lng: (bounds.east + bounds.west) / 2
  }
}

/**
 * 简化轨迹点（Douglas-Peucker算法）
 */
export const simplifyTrack = (points: LatLng[], tolerance: number = 0.0001): LatLng[] => {
  if (points.length <= 2) return points

  const douglasPeucker = (points: LatLng[], tolerance: number): LatLng[] => {
    if (points.length <= 2) return points

    let maxDistance = 0
    let maxIndex = 0
    const start = points[0]
    const end = points[points.length - 1]

    for (let i = 1; i < points.length - 1; i++) {
      const distance = perpendicularDistance(points[i], start, end)
      if (distance > maxDistance) {
        maxDistance = distance
        maxIndex = i
      }
    }

    if (maxDistance > tolerance) {
      const left = douglasPeucker(points.slice(0, maxIndex + 1), tolerance)
      const right = douglasPeucker(points.slice(maxIndex), tolerance)
      return [...left.slice(0, -1), ...right]
    } else {
      return [start, end]
    }
  }

  return douglasPeucker(points, tolerance)
}

/**
 * 计算点到线段的垂直距离
 */
const perpendicularDistance = (point: LatLng, lineStart: LatLng, lineEnd: LatLng): number => {
  const A = point.lat - lineStart.lat
  const B = point.lng - lineStart.lng
  const C = lineEnd.lat - lineStart.lat
  const D = lineEnd.lng - lineStart.lng

  const dot = A * C + B * D
  const lenSq = C * C + D * D
  
  if (lenSq === 0) {
    return calculateDistance(point, lineStart)
  }

  const param = dot / lenSq
  let closestPoint: LatLng

  if (param < 0) {
    closestPoint = lineStart
  } else if (param > 1) {
    closestPoint = lineEnd
  } else {
    closestPoint = {
      lat: lineStart.lat + param * C,
      lng: lineStart.lng + param * D
    }
  }

  return calculateDistance(point, closestPoint)
}
