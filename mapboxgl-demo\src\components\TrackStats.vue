<template>
  <div class="track-stats">
    <n-card size="small" class="stats-card">
      <template #header>
        <span>📊 实时数据</span>
      </template>

      <n-space vertical size="small" v-if="currentStats">
        <!-- 进度信息 -->
        <div class="stat-item">
          <div class="stat-label">进度</div>
          <div class="stat-value">
            {{ Math.round(progress * 100) }}%
            <span class="stat-unit">({{ currentPointIndex + 1 }}/{{ totalPoints }})</span>
          </div>
        </div>

        <!-- 距离信息 -->
        <div class="stat-item">
          <div class="stat-label">已行进距离</div>
          <div class="stat-value">
            {{ formatDistance(currentStats.distance) }}
          </div>
        </div>

        <!-- 海拔信息 -->
        <div class="stat-item" v-if="currentStats.elevation">
          <div class="stat-label">当前海拔</div>
          <div class="stat-value">
            {{ Math.round(currentStats.elevation) }}
            <span class="stat-unit">m</span>
          </div>
        </div>

        <!-- 速度信息 -->
        <div class="stat-item" v-if="currentStats.speed">
          <div class="stat-label">当前速度</div>
          <div class="stat-value">
            {{ formatSpeed(currentStats.speed) }}
          </div>
        </div>

        <!-- 心率信息 -->
        <div class="stat-item" v-if="currentStats.heartRate">
          <div class="stat-label">心率</div>
          <div class="stat-value">
            {{ Math.round(currentStats.heartRate) }}
            <span class="stat-unit">bpm</span>
          </div>
        </div>

        <!-- 时间信息 -->
        <div class="stat-item" v-if="currentStats.time">
          <div class="stat-label">时间</div>
          <div class="stat-value">
            {{ formatTime(currentStats.time) }}
          </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
          <n-progress 
            :percentage="Math.round(progress * 100)"
            :show-indicator="false"
            color="#ff6b6b"
            rail-color="rgba(255, 255, 255, 0.2)"
          />
        </div>
      </n-space>

      <div v-else class="no-data">
        <span>暂无轨迹数据</span>
      </div>

      <template #action>
        <n-space>
          <n-button @click="$emit('reset')" size="small">
            重置
          </n-button>
          <n-button @click="$emit('close')" size="small" quaternary>
            关闭
          </n-button>
        </n-space>
      </template>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NCard, NSpace, NButton, NProgress } from 'naive-ui'
import { useMeasurementStore } from '@/stores/measurement'

// Props
interface Props {
  currentStats?: {
    distance: number
    elevation: number
    speed: number
    heartRate?: number
    time: number
  } | null
  progress: number
  currentPointIndex: number
  totalPoints: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  reset: []
  close: []
}>()

// Stores
const measurementStore = useMeasurementStore()

// 格式化函数
const formatDistance = (meters: number) => {
  return measurementStore.formatDistance(meters)
}

const formatSpeed = (metersPerSecond: number) => {
  return measurementStore.formatSpeed(metersPerSecond)
}

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
.track-stats {
  user-select: none;
}

.stats-card {
  min-width: 200px;
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.8);
  color: white;
}

.stats-card :deep(.n-card__content) {
  padding: 16px;
}

.stats-card :deep(.n-card__header) {
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.stat-unit {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: normal;
}

.progress-bar {
  margin-top: 8px;
}

.no-data {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  padding: 20px 0;
}

/* 浅色主题适配 */
.light .stats-card {
  background: rgba(255, 255, 255, 0.95);
  color: var(--n-text-color-1);
}

.light .stats-card :deep(.n-card__header) {
  color: var(--n-text-color-1);
  border-bottom: 1px solid var(--n-border-color);
}

.light .stat-label {
  color: var(--n-text-color-2);
}

.light .stat-value {
  color: var(--n-text-color-1);
}

.light .stat-unit {
  color: var(--n-text-color-3);
}

.light .no-data {
  color: var(--n-text-color-3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-card {
    min-width: 180px;
  }
  
  .stat-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
}
</style>
