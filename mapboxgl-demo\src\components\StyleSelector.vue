<template>
  <div class="style-selector">
    <n-card size="small" class="selector-card">
      <template #header>
        <span>🗺️ 地图样式</span>
      </template>

      <n-space vertical size="small">
        <n-radio-group 
          v-model:value="selectedStyle" 
          @update:value="handleStyleChange"
          size="small"
        >
          <n-space vertical size="small">
            <n-radio
              v-for="style in availableStyles"
              :key="style.value"
              :value="style.value"
              :label="style.label"
            >
              <template #default>
                <n-space align="center" size="small">
                  <span>{{ style.icon }}</span>
                  <span>{{ style.label }}</span>
                </n-space>
              </template>
            </n-radio>
          </n-space>
        </n-radio-group>

        <!-- 样式预览 -->
        <div class="style-preview" v-if="currentStyleInfo">
          <n-descriptions :column="1" size="small">
            <n-descriptions-item label="当前样式">
              {{ currentStyleInfo.label }}
            </n-descriptions-item>
            <n-descriptions-item label="适用场景">
              {{ currentStyleInfo.description }}
            </n-descriptions-item>
          </n-descriptions>
        </div>
      </n-space>

      <template #action>
        <n-space>
          <n-button @click="resetStyle" size="small">
            重置
          </n-button>
          <n-button @click="$emit('close')" size="small" quaternary>
            关闭
          </n-button>
        </n-space>
      </template>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  NCard,
  NSpace,
  NRadioGroup,
  NRadio,
  NDescriptions,
  NDescriptionsItem,
  NButton
} from 'naive-ui'
import type { MapStyle } from '@/types'

// Props
interface Props {
  currentStyle: MapStyle
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  styleChange: [style: MapStyle]
  close: []
}>()

// 状态
const selectedStyle = ref<MapStyle>(props.currentStyle)

// 可用样式配置
const availableStyles = [
  {
    value: 'satellite' as MapStyle,
    label: '卫星影像',
    icon: '🛰️',
    description: '高清卫星图像，适合查看地形地貌'
  },
  {
    value: 'satellite-streets' as MapStyle,
    label: '卫星街道',
    icon: '🌍',
    description: '卫星图像 + 街道标注，兼顾细节和导航'
  },
  {
    value: 'streets' as MapStyle,
    label: '街道地图',
    icon: '🏙️',
    description: '经典街道地图，适合城市导航'
  },
  {
    value: 'outdoors' as MapStyle,
    label: '户外地图',
    icon: '🏔️',
    description: '户外活动专用，突出地形和步道'
  },
  {
    value: 'light' as MapStyle,
    label: '浅色主题',
    icon: '☀️',
    description: '简洁浅色设计，适合数据可视化'
  },
  {
    value: 'dark' as MapStyle,
    label: '深色主题',
    icon: '🌙',
    description: '深色主题，护眼且现代感强'
  }
]

// 当前样式信息
const currentStyleInfo = computed(() => {
  return availableStyles.find(style => style.value === selectedStyle.value)
})

// 监听props变化
watch(
  () => props.currentStyle,
  (newStyle) => {
    selectedStyle.value = newStyle
  }
)

// 事件处理
const handleStyleChange = (style: MapStyle) => {
  emit('styleChange', style)
  console.log(`地图样式切换为: ${style}`)
}

const resetStyle = () => {
  selectedStyle.value = 'satellite'
  emit('styleChange', 'satellite')
  console.log('地图样式已重置为卫星影像')
}
</script>

<style scoped>
.style-selector {
  user-select: none;
}

.selector-card {
  min-width: 240px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.selector-card :deep(.n-card__content) {
  padding: 16px;
}

.style-preview {
  padding: 8px;
  background: var(--n-color-2);
  border-radius: 6px;
  font-size: 12px;
}

/* 深色主题适配 */
.dark .selector-card {
  background: rgba(0, 0, 0, 0.85);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selector-card {
    min-width: 200px;
  }
}
</style>
