/**
 * 地图引擎核心类
 * 封装MapboxGL的核心功能，提供统一的API接口
 */

import mapboxgl, { Map as MapboxMap } from 'mapbox-gl'
import type { 
  MapConfig, 
  MapState, 
  LayerConfig, 
  SourceConfig, 
  TerrainConfig, 
  SkyConfig,
  IMapEngine,
  MapEventData
} from '@/types/map'
import type { LngLat, Bounds } from '@/types/common'

export class MapEngine implements IMapEngine {
  public map: MapboxMap | null = null
  public isInitialized = false
  
  private eventHandlers = new Map<string, Set<Function>>()
  private container: HTMLElement | null = null

  /**
   * 初始化地图
   */
  async initialize(config: MapConfig): Promise<void> {
    try {
      // 设置访问令牌
      mapboxgl.accessToken = config.accessToken
      
      // 创建地图实例
      this.map = new mapboxgl.Map({
        container: config.container,
        style: config.style,
        center: config.center,
        zoom: config.zoom,
        pitch: config.pitch,
        bearing: config.bearing,
        minZoom: config.minZoom,
        maxZoom: config.maxZoom,
        maxBounds: config.maxBounds ? [
          [config.maxBounds.west, config.maxBounds.south],
          [config.maxBounds.east, config.maxBounds.north]
        ] : undefined
      })

      // 等待地图加载完成
      await new Promise<void>((resolve, reject) => {
        this.map!.on('load', () => resolve())
        this.map!.on('error', (error) => reject(error))
      })

      // 设置容器引用
      if (typeof config.container === 'string') {
        this.container = document.getElementById(config.container)
      } else {
        this.container = config.container
      }

      // 绑定基础事件
      this.bindEvents()
      
      this.isInitialized = true
      console.log('✅ 地图引擎初始化完成')
      
    } catch (error) {
      console.error('❌ 地图引擎初始化失败:', error)
      throw error
    }
  }

  /**
   * 销毁地图
   */
  destroy(): void {
    if (this.map) {
      this.map.remove()
      this.map = null
    }
    this.eventHandlers.clear()
    this.isInitialized = false
    console.log('🗑️ 地图引擎已销毁')
  }

  /**
   * 飞行到指定位置
   */
  flyTo(center: mapboxgl.LngLatLike, zoom?: number, options: any = {}): void {
    if (!this.map) return
    
    this.map.flyTo({
      center,
      zoom: zoom ?? this.map.getZoom(),
      ...options
    })
  }

  /**
   * 跳转到指定位置
   */
  jumpTo(center: mapboxgl.LngLatLike, zoom?: number, options: any = {}): void {
    if (!this.map) return
    
    this.map.jumpTo({
      center,
      zoom: zoom ?? this.map.getZoom(),
      ...options
    })
  }

  /**
   * 适应边界
   */
  fitBounds(bounds: Bounds, options: any = {}): void {
    if (!this.map) return
    
    this.map.fitBounds([
      [bounds.west, bounds.south],
      [bounds.east, bounds.north]
    ], options)
  }

  /**
   * 设置地图样式
   */
  setStyle(style: string | mapboxgl.StyleSpecification): void {
    if (!this.map) return
    this.map.setStyle(style)
  }

  /**
   * 获取地图样式
   */
  getStyle(): mapboxgl.StyleSpecification | undefined {
    return this.map?.getStyle()
  }

  /**
   * 添加图层
   */
  addLayer(layer: LayerConfig): void {
    if (!this.map) return
    
    try {
      this.map.addLayer({
        id: layer.id,
        type: layer.type as any,
        source: layer.source,
        'source-layer': layer.sourceLayer,
        layout: layer.layout,
        paint: layer.paint,
        filter: layer.filter,
        minzoom: layer.minzoom,
        maxzoom: layer.maxzoom
      })
    } catch (error) {
      console.error(`添加图层失败 [${layer.id}]:`, error)
    }
  }

  /**
   * 移除图层
   */
  removeLayer(layerId: string): void {
    if (!this.map) return
    
    try {
      if (this.map.getLayer(layerId)) {
        this.map.removeLayer(layerId)
      }
    } catch (error) {
      console.error(`移除图层失败 [${layerId}]:`, error)
    }
  }

  /**
   * 更新图层
   */
  updateLayer(layerId: string, updates: Partial<LayerConfig>): void {
    if (!this.map || !this.map.getLayer(layerId)) return
    
    try {
      if (updates.layout) {
        Object.entries(updates.layout).forEach(([key, value]) => {
          this.map!.setLayoutProperty(layerId, key, value)
        })
      }
      
      if (updates.paint) {
        Object.entries(updates.paint).forEach(([key, value]) => {
          this.map!.setPaintProperty(layerId, key, value)
        })
      }
      
      if (updates.filter) {
        this.map.setFilter(layerId, updates.filter)
      }
    } catch (error) {
      console.error(`更新图层失败 [${layerId}]:`, error)
    }
  }

  /**
   * 添加数据源
   */
  addSource(sourceId: string, source: SourceConfig): void {
    if (!this.map) return
    
    try {
      this.map.addSource(sourceId, source as any)
    } catch (error) {
      console.error(`添加数据源失败 [${sourceId}]:`, error)
    }
  }

  /**
   * 移除数据源
   */
  removeSource(sourceId: string): void {
    if (!this.map) return
    
    try {
      if (this.map.getSource(sourceId)) {
        this.map.removeSource(sourceId)
      }
    } catch (error) {
      console.error(`移除数据源失败 [${sourceId}]:`, error)
    }
  }

  /**
   * 更新数据源
   */
  updateSource(sourceId: string, data: any): void {
    if (!this.map) return
    
    try {
      const source = this.map.getSource(sourceId) as mapboxgl.GeoJSONSource
      if (source && source.setData) {
        source.setData(data)
      }
    } catch (error) {
      console.error(`更新数据源失败 [${sourceId}]:`, error)
    }
  }

  /**
   * 启用3D地形
   */
  enableTerrain(config: TerrainConfig): void {
    if (!this.map) return
    
    try {
      this.map.setTerrain({
        source: config.source,
        exaggeration: config.exaggeration
      })
    } catch (error) {
      console.error('启用3D地形失败:', error)
    }
  }

  /**
   * 禁用3D地形
   */
  disableTerrain(): void {
    if (!this.map) return
    
    try {
      this.map.setTerrain(null)
    } catch (error) {
      console.error('禁用3D地形失败:', error)
    }
  }

  /**
   * 设置天空
   */
  setSky(config: SkyConfig): void {
    if (!this.map) return
    
    try {
      this.map.setSky(config as any)
    } catch (error) {
      console.error('设置天空失败:', error)
    }
  }

  /**
   * 获取当前地图状态
   */
  getState(): MapState {
    if (!this.map) {
      throw new Error('地图未初始化')
    }

    const center = this.map.getCenter()
    const bounds = this.map.getBounds()
    
    return {
      center: { lng: center.lng, lat: center.lat },
      zoom: this.map.getZoom(),
      pitch: this.map.getPitch(),
      bearing: this.map.getBearing(),
      bounds: {
        north: bounds.getNorth(),
        south: bounds.getSouth(),
        east: bounds.getEast(),
        west: bounds.getWest()
      },
      isLoaded: this.map.loaded(),
      isMoving: this.map.isMoving(),
      isZooming: this.map.isZooming(),
      isRotating: this.map.isRotating()
    }
  }

  /**
   * 绑定事件
   */
  on(event: string, handler: (data: MapEventData) => void): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }
    this.eventHandlers.get(event)!.add(handler)
    
    if (this.map) {
      this.map.on(event as any, handler as any)
    }
  }

  /**
   * 解绑事件
   */
  off(event: string, handler: (data: MapEventData) => void): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.eventHandlers.delete(event)
      }
    }
    
    if (this.map) {
      this.map.off(event as any, handler as any)
    }
  }

  /**
   * 绑定基础事件
   */
  private bindEvents(): void {
    if (!this.map) return

    // 地图移动事件
    this.map.on('move', () => {
      this.emit('stateChange', this.getState())
    })

    // 地图缩放事件
    this.map.on('zoom', () => {
      this.emit('stateChange', this.getState())
    })

    // 地图旋转事件
    this.map.on('rotate', () => {
      this.emit('stateChange', this.getState())
    })
  }

  /**
   * 触发自定义事件
   */
  private emit(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => handler(data))
    }
  }
}
