/**
 * 地图相关的组合式函数
 * 封装地图初始化、状态管理和事件处理逻辑
 */

import { ref, onUnmounted, type Ref } from 'vue'
import { MapEngine } from '@/core/map/MapEngine'
import { useMapStore } from '@/stores/mapStore'
import type { MapConfig, MapEventData } from '@/types/map'
import type { LngLat } from '@/types/common'

export function useMap(container: Ref<HTMLElement | null>) {
  const mapStore = useMapStore()
  
  // 响应式状态
  const mapEngine = ref<MapEngine | null>(null)
  const isInitialized = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  /**
   * 初始化地图
   */
  const initializeMap = async (config: Partial<MapConfig> & { accessToken: string }) => {
    if (!container.value) {
      throw new Error('地图容器未找到')
    }

    if (isInitialized.value) {
      console.warn('地图已经初始化')
      return
    }

    isLoading.value = true
    error.value = null

    try {
      // 创建地图引擎实例
      mapEngine.value = new MapEngine()

      // 合并默认配置
      const fullConfig: MapConfig = {
        container: container.value,
        style: 'mapbox://styles/mapbox/streets-v12',
        center: [116.4074, 39.9042],
        zoom: 10,
        pitch: 0,
        bearing: 0,
        minZoom: 1,
        maxZoom: 20,
        accessToken: config.accessToken,
        ...config
      }

      // 初始化地图引擎
      await mapEngine.value.initialize(fullConfig)

      // 绑定事件监听器
      bindEventListeners()

      // 更新状态
      isInitialized.value = true
      mapStore.updateState({ isLoaded: true })

      console.log('✅ 地图初始化成功')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误'
      error.value = errorMessage
      console.error('❌ 地图初始化失败:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 绑定事件监听器
   */
  const bindEventListeners = () => {
    if (!mapEngine.value) return

    // 地图状态变化事件
    mapEngine.value.on('stateChange', (state) => {
      mapStore.updateState(state)
    })

    // 地图移动事件
    mapEngine.value.on('move', (data: MapEventData) => {
      mapStore.setCenter(data.lngLat)
    })

    // 地图缩放事件
    mapEngine.value.on('zoom', () => {
      if (mapEngine.value?.map) {
        mapStore.setZoom(mapEngine.value.map.getZoom())
      }
    })

    // 地图旋转事件
    mapEngine.value.on('rotate', () => {
      if (mapEngine.value?.map) {
        mapStore.setBearing(mapEngine.value.map.getBearing())
        mapStore.setPitch(mapEngine.value.map.getPitch())
      }
    })

    // 地图点击事件
    mapEngine.value.on('click', (data: MapEventData) => {
      console.log('地图点击:', data.lngLat)
    })

    // 地图加载完成事件
    mapEngine.value.on('load', () => {
      console.log('🗺️ 地图加载完成')
      mapStore.updateState({ isLoaded: true })
    })

    // 地图错误事件
    mapEngine.value.on('error', (error: any) => {
      console.error('地图错误:', error)
    })
  }

  /**
   * 飞行到指定位置
   */
  const flyTo = (center: LngLat, zoom?: number, options?: any) => {
    if (!mapEngine.value) return
    
    mapEngine.value.flyTo([center.lng, center.lat], zoom, {
      duration: 2000,
      essential: true,
      ...options
    })
  }

  /**
   * 跳转到指定位置
   */
  const jumpTo = (center: LngLat, zoom?: number, options?: any) => {
    if (!mapEngine.value) return
    
    mapEngine.value.jumpTo([center.lng, center.lat], zoom, options)
  }

  /**
   * 适应边界
   */
  const fitBounds = (bounds: [[number, number], [number, number]], options?: any) => {
    if (!mapEngine.value) return
    
    mapEngine.value.fitBounds({
      west: bounds[0][0],
      south: bounds[0][1],
      east: bounds[1][0],
      north: bounds[1][1]
    }, {
      padding: 50,
      duration: 2000,
      ...options
    })
  }

  /**
   * 设置地图样式
   */
  const setStyle = (style: string) => {
    if (!mapEngine.value) return
    
    mapEngine.value.setStyle(style)
  }

  /**
   * 添加图层
   */
  const addLayer = (layer: any) => {
    if (!mapEngine.value) return
    
    mapEngine.value.addLayer(layer)
  }

  /**
   * 移除图层
   */
  const removeLayer = (layerId: string) => {
    if (!mapEngine.value) return
    
    mapEngine.value.removeLayer(layerId)
  }

  /**
   * 添加数据源
   */
  const addSource = (sourceId: string, source: any) => {
    if (!mapEngine.value) return
    
    mapEngine.value.addSource(sourceId, source)
  }

  /**
   * 移除数据源
   */
  const removeSource = (sourceId: string) => {
    if (!mapEngine.value) return
    
    mapEngine.value.removeSource(sourceId)
  }

  /**
   * 更新数据源
   */
  const updateSource = (sourceId: string, data: any) => {
    if (!mapEngine.value) return
    
    mapEngine.value.updateSource(sourceId, data)
  }

  /**
   * 启用3D地形
   */
  const enableTerrain = (source: string, exaggeration = 1.5) => {
    if (!mapEngine.value) return
    
    mapEngine.value.enableTerrain({ source, exaggeration })
  }

  /**
   * 禁用3D地形
   */
  const disableTerrain = () => {
    if (!mapEngine.value) return
    
    mapEngine.value.disableTerrain()
  }

  /**
   * 设置天空
   */
  const setSky = (config: any) => {
    if (!mapEngine.value) return
    
    mapEngine.value.setSky(config)
  }

  /**
   * 获取当前地图状态
   */
  const getMapState = () => {
    if (!mapEngine.value) return null
    
    return mapEngine.value.getState()
  }

  /**
   * 截图
   */
  const takeScreenshot = (): string | null => {
    if (!mapEngine.value?.map) return null
    
    const canvas = mapEngine.value.map.getCanvas()
    return canvas.toDataURL('image/png')
  }

  /**
   * 重置视图
   */
  const resetView = () => {
    flyTo({ lng: 116.4074, lat: 39.9042 }, 10, {
      pitch: 0,
      bearing: 0
    })
  }

  /**
   * 销毁地图
   */
  const destroyMap = () => {
    if (mapEngine.value) {
      mapEngine.value.destroy()
      mapEngine.value = null
      isInitialized.value = false
      mapStore.updateState({ isLoaded: false })
      console.log('🗑️ 地图已销毁')
    }
  }

  // 组件卸载时自动销毁地图
  onUnmounted(() => {
    destroyMap()
  })

  return {
    // 状态
    mapEngine,
    isInitialized,
    isLoading,
    error,

    // 方法
    initializeMap,
    flyTo,
    jumpTo,
    fitBounds,
    setStyle,
    addLayer,
    removeLayer,
    addSource,
    removeSource,
    updateSource,
    enableTerrain,
    disableTerrain,
    setSky,
    getMapState,
    takeScreenshot,
    resetView,
    destroyMap
  }
}
